<x-admin::layouts>
    <x-slot:title>
        @lang('sales::app.reports.forecast')
    </x-slot>

    <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="flex cursor-pointer items-center">
                    <x-admin::breadcrumbs name="sales.reports.forecast" />
                </div>

                <div class="text-xl font-bold dark:text-white">
                    Sales Forecast
                </div>

                <div class="text-gray-600 dark:text-gray-300">
                    Analyze sales trends and forecast future performance based on historical data
                </div>
            </div>

            <div class="flex items-center gap-x-2.5">
                <a href="{{ route('admin.sales.reports.index') }}" class="secondary-button">
                    <span class="icon-arrow-left text-2xl"></span>
                    Back to Reports
                </a>
            </div>
        </div>

        <!-- Forecast Chart -->
        <div class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
            <div class="mb-6 flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sales Forecast Projection</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Predictive analysis based on historical performance</p>
                </div>
                <div class="flex space-x-2">
                    <button class="rounded-md bg-blue-100 px-3 py-1 text-sm font-medium text-blue-700 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800">
                        3M
                    </button>
                    <button class="rounded-md bg-gray-100 px-3 py-1 text-sm font-medium text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        6M
                    </button>
                </div>
            </div>

            <div class="h-80">
                <canvas id="forecastChart"></canvas>
            </div>
        </div>

        <!-- Forecast Insights -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <!-- Key Metrics -->
            <div class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
                <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Forecast Insights</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-300">Projected Growth Rate</span>
                        <span class="text-sm font-medium text-green-600 dark:text-green-400">+12.5%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-300">Confidence Level</span>
                        <span class="text-sm font-medium text-blue-600 dark:text-blue-400">85%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-300">Next Quarter Target</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">$125,000</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-300">Seasonal Factor</span>
                        <span class="text-sm font-medium text-orange-600 dark:text-orange-400">High</span>
                    </div>
                </div>
            </div>

            <!-- Planned Features -->
            <div class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
                <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Advanced Features</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <span class="icon-success mr-3 text-green-500"></span>
                        <span class="text-sm text-gray-600 dark:text-gray-300">Historical trend analysis</span>
                    </div>
                    <div class="flex items-center">
                        <span class="icon-success mr-3 text-green-500"></span>
                        <span class="text-sm text-gray-600 dark:text-gray-300">Seasonal pattern recognition</span>
                    </div>
                    <div class="flex items-center">
                        <span class="icon-success mr-3 text-green-500"></span>
                        <span class="text-sm text-gray-600 dark:text-gray-300">Performance-based projections</span>
                    </div>
                    <div class="flex items-center">
                        <span class="icon-success mr-3 text-green-500"></span>
                        <span class="text-sm text-gray-600 dark:text-gray-300">Confidence intervals</span>
                    </div>
                    <div class="flex items-center">
                        <span class="icon-success mr-3 text-green-500"></span>
                        <span class="text-sm text-gray-600 dark:text-gray-300">Multiple forecasting models</span>
                    </div>
                </div>

                <div class="mt-6">
                    <a href="{{ route('admin.sales.reports.target_vs_actual') }}" class="primary-button w-full">
                        View Current Performance
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-admin::layouts>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('forecastChart').getContext('2d');

    // Sample forecast data
    const forecastData = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep'],
        historical: [45000, 52000, 58000, 61000, 59000, 63000, null, null, null],
        forecast: [null, null, null, null, null, 63000, 68000, 72000, 75000],
        confidence: [null, null, null, null, null, [58000, 68000], [63000, 73000], [67000, 77000], [70000, 80000]]
    };

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: forecastData.labels,
            datasets: [{
                label: 'Historical Performance',
                data: forecastData.historical,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                pointRadius: 5,
                pointHoverRadius: 7
            }, {
                label: 'Forecast',
                data: forecastData.forecast,
                borderColor: 'rgb(16, 185, 129)',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                borderDash: [5, 5],
                fill: false,
                tension: 0.4,
                pointRadius: 5,
                pointHoverRadius: 7
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': $' + (context.parsed.y ? context.parsed.y.toLocaleString() : 'N/A');
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Month'
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Sales Amount ($)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
});
</script>
@endpush
