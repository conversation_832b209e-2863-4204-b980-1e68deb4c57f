<?php

namespace Webkul\Sales\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Sales\Repositories\SalesTargetRepository;
use Webkul\User\Repositories\UserRepository;

class PerformanceController extends Controller
{
    public function __construct(
        protected SalesTargetRepository $salesTargetRepository,
        protected UserRepository $userRepository
    ) {
    }

    /**
     * Display performance tracking page.
     */
    public function index(Request $request): View
    {
        $filters = $request->only(['financial_year', 'user_id', 'team_id']);
        
        // Get performance data based on filters
        $performanceData = $this->getPerformanceData($filters);
        
        $users = $this->userRepository->all(['id', 'name']);
        
        // Get available financial years
        $financialYears = $this->salesTargetRepository
            ->distinct()
            ->pluck('financial_year')
            ->sort()
            ->values();

        return view('sales::performance.index', compact(
            'performanceData',
            'users',
            'financialYears',
            'filters'
        ));
    }

    /**
     * Get user-specific performance.
     */
    public function userPerformance(int $id): View
    {
        $user = $this->userRepository->findOrFail($id);
        
        $targets = $this->salesTargetRepository->getUserTargets($id);
        
        return view('sales::performance.user', compact('user', 'targets'));
    }

    /**
     * Get team-specific performance.
     */
    public function teamPerformance(int $id): View
    {
        // Implementation for team performance
        return view('sales::performance.team');
    }

    /**
     * Get performance data via API.
     */
    public function getPerformanceData(array $filters = []): JsonResponse|array
    {
        $summary = $this->salesTargetRepository->getPerformanceSummary($filters);
        
        if (request()->expectsJson()) {
            return response()->json($summary);
        }
        
        return $summary;
    }
}
