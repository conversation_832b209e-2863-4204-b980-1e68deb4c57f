<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ core()->getCurrentLocale()->direction }}">
    <head>
        <title>{{ config('app.name') }}</title>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        @stack('meta')

        @if ($favicon = core()->getConfigData('general.design.admin_logo.favicon'))
            <link rel="icon" sizes="16x16" href="{{ Storage::url($favicon) }}" />
        @else
            <link rel="icon" sizes="16x16" href="{{ asset('vendor/webkul/admin/assets/images/favicon.ico') }}" />
        @endif

        @vite(['packages/Webkul/Admin/src/Resources/assets/css/app.css', 'packages/Webkul/Admin/src/Resources/assets/js/app.js'])

        <style>
            .icon-import:before {
                content: "\ea8d";
                font-family: 'icomoon' !important;
            }
        </style>

        @stack('styles')
    </head>

    <body>
        {!! view_render_event('bagisto.admin.layout.body.before') !!}

        <div id="app">
            <flash-wrapper ref='flashes'></flash-wrapper>

            {!! view_render_event('bagisto.admin.layout.content.before') !!}

            <router-view></router-view>

            {!! view_render_event('bagisto.admin.layout.content.after') !!}
        </div>

        {!! view_render_event('bagisto.admin.layout.body.after') !!}

        @stack('scripts')
    </body>
</html>
