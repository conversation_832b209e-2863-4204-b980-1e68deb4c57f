@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.targets.create') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.targets.create') }}</h1>
                <p>{{ __('sales::app.targets.create-description') }}</p>
            </div>
            
            <div class="page-action">
                <a href="{{ route('admin.sales.targets.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Targets
                </a>
            </div>
        </div>

        <x-admin::form
            :action="route('admin.sales.targets.store')"
            method="POST"
        >
            <div class="flex gap-[16px] justify-between items-center max-sm:flex-wrap">
                <p class="text-[20px] text-gray-800 dark:text-white font-bold">
                    {{ __('sales::app.targets.create.title') }}
                </p>

                <div class="flex gap-x-[10px] items-center">
                    <a href="{{ route('admin.sales.targets.index') }}" class="transparent-button hover:bg-gray-200 dark:hover:bg-gray-800 dark:text-white">
                        {{ __('sales::app.targets.create.back-btn') }}
                    </a>

                    <button type="submit" class="primary-button">
                        {{ __('sales::app.targets.create.save-btn') }}
                    </button>
                </div>
            </div>

            <div class="flex gap-[16px] justify-between items-center mt-[28px] max-sm:flex-wrap">
                <div class="flex flex-col gap-[8px] w-full">
                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.create.user') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="select"
                            name="user_id"
                            :value="old('user_id')"
                            :options="$users->pluck('name', 'id')"
                            rules="required"
                            :label="__('sales::app.targets.create.user')"
                            :placeholder="__('sales::app.targets.create.select-user')"
                        />

                        <x-admin::form.control-group.error control-name="user_id" />
                    </x-admin::form.control-group>

                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.create.financial-year') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="select"
                            name="financial_year"
                            :value="old('financial_year')"
                            :options="$financialYears"
                            rules="required"
                            :label="__('sales::app.targets.create.financial-year')"
                            :placeholder="__('sales::app.targets.create.select-financial-year')"
                        />

                        <x-admin::form.control-group.error control-name="financial_year" />
                    </x-admin::form.control-group>

                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.create.period-type') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="select"
                            name="period_type"
                            :value="old('period_type')"
                            :options="[
                                ['value' => 'annual', 'label' => __('sales::app.targets.create.annual')],
                                ['value' => 'half_yearly', 'label' => __('sales::app.targets.create.half-yearly')],
                                ['value' => 'quarterly', 'label' => __('sales::app.targets.create.quarterly')],
                                ['value' => 'monthly', 'label' => __('sales::app.targets.create.monthly')],
                                ['value' => 'custom', 'label' => __('sales::app.targets.create.custom')],
                            ]"
                            rules="required"
                            :label="__('sales::app.targets.create.period-type')"
                            :placeholder="__('sales::app.targets.create.select-period-type')"
                        />

                        <x-admin::form.control-group.error control-name="period_type" />
                    </x-admin::form.control-group>

                    <div id="period_value_container">
                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                {{ __('sales::app.targets.create.period-value') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control
                                type="select"
                                name="period_value"
                                :value="old('period_value')"
                                :options="[]"
                                :label="__('sales::app.targets.create.period-value')"
                                :placeholder="__('sales::app.targets.create.select-period')"
                            />

                            <x-admin::form.control-group.error control-name="period_value" />
                        </x-admin::form.control-group>
                    </div>

                    <div id="custom_date_range_container" style="display: none;">
                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                {{ __('sales::app.targets.create.start-date') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control
                                type="date"
                                name="start_date"
                                :value="old('start_date')"
                                :label="__('sales::app.targets.create.start-date')"
                            />

                            <x-admin::form.control-group.error control-name="start_date" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                {{ __('sales::app.targets.create.end-date') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control
                                type="date"
                                name="end_date"
                                :value="old('end_date')"
                                :label="__('sales::app.targets.create.end-date')"
                            />

                            <x-admin::form.control-group.error control-name="end_date" />
                        </x-admin::form.control-group>
                    </div>

                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.create.target-value') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="text"
                            name="target_value"
                            :value="old('target_value')"
                            rules="required|numeric"
                            :label="__('sales::app.targets.create.target-value')"
                        />

                        <x-admin::form.control-group.error control-name="target_value" />
                    </x-admin::form.control-group>

                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.create.target-type') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="select"
                            name="target_type"
                            :value="old('target_type')"
                            :options="[
                                ['value' => 'revenue', 'label' => __('sales::app.targets.create.revenue')],
                                ['value' => 'deals', 'label' => __('sales::app.targets.create.deals')],
                                ['value' => 'leads', 'label' => __('sales::app.targets.create.leads')],
                            ]"
                            rules="required"
                            :label="__('sales::app.targets.create.target-type')"
                        />

                        <x-admin::form.control-group.error control-name="target_type" />
                    </x-admin::form.control-group>

                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label>
                            {{ __('sales::app.targets.create.description') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="textarea"
                            name="description"
                            :value="old('description')"
                            :label="__('sales::app.targets.create.description')"
                        />
                    </x-admin::form.control-group>

                    <x-admin::form.control-group>
                        <x-admin::form.control
                            type="checkbox"
                            name="is_active"
                            :value="1"
                            :checked="old('is_active')"
                            :label="__('sales::app.targets.create.is-active')"
                        />
                    </x-admin::form.control-group>
                </div>
            </div>
        </x-admin::form>
    </div>
@stop

@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.targets.create') }}
@stop

@section('content')
    <div class="content">
        <x-admin::form
            :action="route('admin.sales.targets.store')"
            method="POST"
        >
            <div class="flex gap-[16px] justify-between items-center max-sm:flex-wrap">
                <p class="text-[20px] text-gray-800 dark:text-white font-bold">
                    {{ __('sales::app.targets.create.title') }}
                </p>

                <div class="flex gap-x-[10px] items-center">
                    <a href="{{ route('admin.sales.targets.index') }}" class="transparent-button hover:bg-gray-200 dark:hover:bg-gray-800 dark:text-white">
                        {{ __('sales::app.targets.create.back-btn') }}
                    </a>

                    <button type="submit" class="primary-button">
                        {{ __('sales::app.targets.create.save-btn') }}
                    </button>
                </div>
            </div>

            <div class="flex flex-col gap-[8px] w-full mt-[28px]">
                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.create.user') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="select"
                        name="user_id"
                        :value="old('user_id')"
                        :options="$users->pluck('name', 'id')"
                        rules="required"
                        :label="__('sales::app.targets.create.user')"
                        :placeholder="__('sales::app.targets.create.select-user')"
                    />

                    <x-admin::form.control-group.error control-name="user_id" />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.create.financial-year') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="select"
                        name="financial_year"
                        :value="old('financial_year')"
                        :options="$financialYears"
                        rules="required"
                        :label="__('sales::app.targets.create.financial-year')"
                        :placeholder="__('sales::app.targets.create.select-financial-year')"
                    />

                    <x-admin::form.control-group.error control-name="financial_year" />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.create.period-type') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="select"
                        name="period_type"
                        :value="old('period_type')"
                        :options="[
                            ['value' => 'annual', 'label' => __('sales::app.targets.create.annual')],
                            ['value' => 'half_yearly', 'label' => __('sales::app.targets.create.half-yearly')],
                            ['value' => 'quarterly', 'label' => __('sales::app.targets.create.quarterly')],
                            ['value' => 'monthly', 'label' => __('sales::app.targets.create.monthly')],
                            ['value' => 'custom', 'label' => __('sales::app.targets.create.custom')],
                        ]"
                        rules="required"
                        :label="__('sales::app.targets.create.period-type')"
                        :placeholder="__('sales::app.targets.create.select-period-type')"
                    />

                    <x-admin::form.control-group.error control-name="period_type" />
                </x-admin::form.control-group>

                <div id="period_value_container">
                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.create.period-value') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="select"
                            name="period_value"
                            :value="old('period_value')"
                            :options="[]"
                            :label="__('sales::app.targets.create.period-value')"
                            :placeholder="__('sales::app.targets.create.select-period')"
                        />

                        <x-admin::form.control-group.error control-name="period_value" />
                    </x-admin::form.control-group>
                </div>

                <div id="custom_date_range_container" class="flex flex-col gap-[8px]" style="display: none;">
                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.create.start-date') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="date"
                            name="start_date"
                            :value="old('start_date')"
                            :label="__('sales::app.targets.create.start-date')"
                        />

                        <x-admin::form.control-group.error control-name="start_date" />
                    </x-admin::form.control-group>

                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.create.end-date') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="date"
                            name="end_date"
                            :value="old('end_date')"
                            :label="__('sales::app.targets.create.end-date')"
                        />

                        <x-admin::form.control-group.error control-name="end_date" />
                    </x-admin::form.control-group>
                </div>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.create.target-value') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="text"
                        name="target_value"
                        :value="old('target_value')"
                        rules="required|numeric"
                        :label="__('sales::app.targets.create.target-value')"
                    />

                    <x-admin::form.control-group.error control-name="target_value" />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.create.target-type') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="select"
                        name="target_type"
                        :value="old('target_type')"
                        :options="[
                            ['value' => 'revenue', 'label' => __('sales::app.targets.create.revenue')],
                            ['value' => 'deals', 'label' => __('sales::app.targets.create.deals')],
                            ['value' => 'leads', 'label' => __('sales::app.targets.create.leads')],
                        ]"
                        rules="required"
                        :label="__('sales::app.targets.create.target-type')"
                    />

                    <x-admin::form.control-group.error control-name="target_type" />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label>
                        {{ __('sales::app.targets.create.description') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="textarea"
                        name="description"
                        :value="old('description')"
                        :label="__('sales::app.targets.create.description')"
                    />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control
                        type="checkbox"
                        name="is_active"
                        :value="1"
                        :checked="old('is_active', true)"
                        :label="__('sales::app.targets.create.is-active')"
                    />
                </x-admin::form.control-group>
            </div>
        </x-admin::form>
    </div>
@stop

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const periodTypeSelect = document.getElementById('period_type');
    const periodValueContainer = document.getElementById('period_value_container');
    const periodValueSelect = document.getElementById('period_value');
    const customDateRangeContainer = document.getElementById('custom_date_range_container');
    const customDateRangeContainerEnd = document.getElementById('custom_date_range_container_end');

    function updatePeriodValues() {
        const periodType = periodTypeSelect.value;
        periodValueSelect.innerHTML = '<option value="">Select Period</option>';

        if (periodType === 'custom') {
            periodValueContainer.style.display = 'none';
            customDateRangeContainer.style.display = 'flex';
            customDateRangeContainerEnd.style.display = 'flex';
            periodValueSelect.required = false;
            document.getElementById('start_date').required = true;
            document.getElementById('end_date').required = true;
        } else {
            periodValueContainer.style.display = 'block';
            customDateRangeContainer.style.display = 'none';
            customDateRangeContainerEnd.style.display = 'none';
            periodValueSelect.required = true;
            document.getElementById('start_date').required = false;
            document.getElementById('end_date').required = false;
        }

        if (periodType === 'annual') {
            periodValueContainer.style.display = 'none';
            periodValueSelect.required = false;
        } else if (periodType === 'half_yearly') {
            for (let i = 1; i <= 2; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `H${i}`;
                if ('{{ old("period_value") }}' == i) option.selected = true;
                periodValueSelect.appendChild(option);
            }
        } else if (periodType === 'quarterly') {
            for (let i = 1; i <= 4; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `Q${i}`;
                if ('{{ old("period_value") }}' == i) option.selected = true;
                periodValueSelect.appendChild(option);
            }
        } else if (periodType === 'monthly') {
            const months = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];
            months.forEach((month, index) => {
                const option = document.createElement('option');
                option.value = index + 1;
                option.textContent = month;
                if ('{{ old("period_value") }}' == (index + 1)) option.selected = true;
                periodValueSelect.appendChild(option);
            });
        }
    }
    
    periodTypeSelect.addEventListener('change', updatePeriodValues);
    
    // Initialize on page load
    if (periodTypeSelect.value) {
        updatePeriodValues();
    }
});
</script>
@endpush

