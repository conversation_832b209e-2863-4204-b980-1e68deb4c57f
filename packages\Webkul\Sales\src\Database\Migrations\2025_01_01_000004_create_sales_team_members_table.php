<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('crm_sales_team_members', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('sales_team_id')->unsigned();
            $table->integer('user_id')->unsigned();
            $table->enum('role', ['member', 'senior', 'lead'])->default('member');
            $table->date('joined_date');
            $table->date('left_date')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();

            $table->foreign('sales_team_id')->references('id')->on('crm_sales_teams')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            $table->unique(['sales_team_id', 'user_id'], 'unique_crm_team_member');
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('crm_sales_team_members');
    }
};
