<x-admin::layouts>
    <x-slot:title>
        Test Simple View
    </x-slot>

    <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="text-xl font-bold dark:text-white">
                    Simple Test View
                </div>
            </div>
        </div>

        <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
            <h3 class="text-lg font-semibold mb-4">Test Form Components</h3>
            
            <form method="GET">
                <div class="flex gap-4">
                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label>
                            Test Select
                        </x-admin::form.control-group.label>

                        <x-admin::form.control-group.control
                            type="select"
                            name="test_select"
                        >
                            <option value="">Select Option</option>
                            <option value="1">Option 1</option>
                            <option value="2">Option 2</option>
                        </x-admin::form.control-group.control>
                    </x-admin::form.control-group>

                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label>
                            Test Date
                        </x-admin::form.control-group.label>

                        <x-admin::form.control-group.control
                            type="date"
                            name="test_date"
                        />
                    </x-admin::form.control-group>
                </div>
            </form>
        </div>
    </div>
</x-admin::layouts>
