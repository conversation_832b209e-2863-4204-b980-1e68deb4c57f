<?php

use Illuminate\Support\Facades\Route;
use Webkul\Sales\Http\Controllers\SalesController;
use Webkul\Sales\Http\Controllers\TargetController;
use Webkul\Sales\Http\Controllers\PerformanceController;
use Webkul\Sales\Http\Controllers\ReportController;

Route::group(['middleware' => ['web', 'admin_locale', 'user'], 'prefix' => config('app.admin_path')], function () {
    Route::prefix('sales')->group(function () {
        // Sales Dashboard
        Route::get('/', [SalesController::class, 'index'])->name('admin.sales.index');
        
        // Sales Targets
        Route::prefix('targets')->group(function () {
            Route::get('/', [TargetController::class, 'index'])->name('admin.sales.targets.index');
            Route::get('/create', [TargetController::class, 'create'])->name('admin.sales.targets.create');
            Route::post('/create', [TargetController::class, 'store'])->name('admin.sales.targets.store');
            Route::get('/edit/{id}', [TargetController::class, 'edit'])->name('admin.sales.targets.edit');
            Route::put('/edit/{id}', [TargetController::class, 'update'])->name('admin.sales.targets.update');
            Route::delete('/{id}', [TargetController::class, 'destroy'])->name('admin.sales.targets.destroy');
            Route::post('/bulk-create', [TargetController::class, 'bulkCreate'])->name('admin.sales.targets.bulk_create');
            Route::get('/export', [TargetController::class, 'export'])->name('admin.sales.targets.export');
            Route::post('/import', [TargetController::class, 'import'])->name('admin.sales.targets.import');
        });
        
        // Performance Tracking
        Route::prefix('performance')->group(function () {
            Route::get('/', [PerformanceController::class, 'index'])->name('admin.sales.performance.index');
            Route::get('/user/{id}', [PerformanceController::class, 'userPerformance'])->name('admin.sales.performance.user');
            Route::get('/team/{id}', [PerformanceController::class, 'teamPerformance'])->name('admin.sales.performance.team');
            Route::get('/api/data', [PerformanceController::class, 'getPerformanceData'])->name('admin.sales.performance.api.data');
        });
        
        // Reports
        Route::prefix('reports')->group(function () {
            Route::get('/', [ReportController::class, 'index'])->name('admin.sales.reports.index');
            Route::get('/target-vs-actual', [ReportController::class, 'targetVsActual'])->name('admin.sales.reports.target_vs_actual');
            Route::get('/leaderboard', [ReportController::class, 'leaderboard'])->name('admin.sales.reports.leaderboard');
            Route::get('/forecast', [ReportController::class, 'forecast'])->name('admin.sales.reports.forecast');
            Route::get('/export/{type}', [ReportController::class, 'export'])->name('admin.sales.reports.export');
        });

        // Test route
        Route::get('/test', function () {
            return view('sales::test');
        })->name('admin.sales.test');
    });
});
