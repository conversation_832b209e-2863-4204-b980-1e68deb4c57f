@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.reports.title') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.reports.title') }}</h1>
                <p>{{ __('sales::app.reports.description') }}</p>
            </div>
        </div>

        <div class="page-content">
            <div class="row">
                <!-- Target vs Actual Report -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-wrapper bg-primary text-white rounded-circle me-3" style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div>
                                    <h5 class="card-title mb-0">Target vs Actual</h5>
                                    <small class="text-muted">Performance Analysis</small>
                                </div>
                            </div>
                            <p class="card-text">Compare sales targets with actual achievements across different time periods.</p>
                            <a href="{{ route('admin.sales.reports.target_vs_actual') }}" class="btn btn-primary">
                                View Report
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Leaderboard Report -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-wrapper bg-success text-white rounded-circle me-3" style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div>
                                    <h5 class="card-title mb-0">Leaderboard</h5>
                                    <small class="text-muted">Top Performers</small>
                                </div>
                            </div>
                            <p class="card-text">View top performing sales representatives based on achievement percentage.</p>
                            <a href="{{ route('admin.sales.reports.leaderboard') }}" class="btn btn-success">
                                View Report
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Forecast Report -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-wrapper bg-info text-white rounded-circle me-3" style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-chart-area"></i>
                                </div>
                                <div>
                                    <h5 class="card-title mb-0">Sales Forecast</h5>
                                    <small class="text-muted">Future Projections</small>
                                </div>
                            </div>
                            <p class="card-text">Analyze sales trends and forecast future performance based on historical data.</p>
                            <a href="{{ route('admin.sales.reports.forecast') }}" class="btn btn-info">
                                View Report
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <h3 class="text-primary">{{ $totalTargets ?? 0 }}</h3>
                                        <p class="text-muted mb-0">Total Targets</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <h3 class="text-success">{{ $activeUsers ?? 0 }}</h3>
                                        <p class="text-muted mb-0">Active Sales Reps</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <h3 class="text-info">{{ number_format($avgAchievement ?? 0, 1) }}%</h3>
                                        <p class="text-muted mb-0">Avg Achievement</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <h3 class="text-warning">{{ $currentPeriod ?? 'N/A' }}</h3>
                                        <p class="text-muted mb-0">Current Period</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@push('css')
<style>
    .icon-wrapper {
        flex-shrink: 0;
    }
    
    .card {
        transition: transform 0.2s ease-in-out;
        border: 1px solid #e3e6f0;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .stat-item h3 {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .btn {
        border-radius: 0.375rem;
        font-weight: 500;
    }
</style>
@endpush
