@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.performance.title') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.performance.title') }}</h1>
                <p>{{ __('sales::app.performance.description') }}</p>
            </div>
        </div>

        <!-- Filters -->
        <div class="page-content">
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.sales.performance.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="financial_year">Financial Year</label>
                                    <select name="financial_year" id="financial_year" class="form-control">
                                        <option value="">All Years</option>
                                        @if(isset($financialYears))
                                            @foreach($financialYears as $year)
                                                <option value="{{ $year }}" {{ ($filters['financial_year'] ?? '') == $year ? 'selected' : '' }}>
                                                    {{ $year }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="period_type">Period Type</label>
                                    <select name="period_type" id="period_type" class="form-control">
                                        <option value="">All Periods</option>
                                        <option value="quarter" {{ ($filters['period_type'] ?? '') == 'quarter' ? 'selected' : '' }}>Quarter</option>
                                        <option value="month" {{ ($filters['period_type'] ?? '') == 'month' ? 'selected' : '' }}>Month</option>
                                        <option value="half_year" {{ ($filters['period_type'] ?? '') == 'half_year' ? 'selected' : '' }}>Half Year</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="user_id">Sales Representative</label>
                                    <select name="user_id" id="user_id" class="form-control">
                                        <option value="">All Users</option>
                                        @if(isset($users))
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}" {{ ($filters['user_id'] ?? '') == $user->id ? 'selected' : '' }}>
                                                    {{ $user->name }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter"></i> Apply Filters
                                        </button>
                                        <a href="{{ route('admin.sales.performance.index') }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Performance Overview -->
            @if(isset($performanceData) && count($performanceData) > 0)
                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>{{ count($performanceData) }}</h4>
                                        <p class="mb-0">Active Performers</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>{{ number_format($performanceData->avg('achievement_percentage'), 1) }}%</h4>
                                        <p class="mb-0">Avg Achievement</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-line fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>{{ $performanceData->where('achievement_percentage', '>=', 100)->count() }}</h4>
                                        <p class="mb-0">Targets Exceeded</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-trophy fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>{{ number_format($performanceData->sum('target_amount'), 0) }}</h4>
                                        <p class="mb-0">Total Targets</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-bullseye fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Individual Performance</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sales Rep</th>
                                        <th>Period</th>
                                        <th>Target</th>
                                        <th>Actual</th>
                                        <th>Achievement</th>
                                        <th>Performance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($performanceData as $data)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center">
                                                        {{ substr($data->user_name ?? 'N', 0, 1) }}
                                                    </div>
                                                    <strong>{{ $data->user_name ?? 'N/A' }}</strong>
                                                </div>
                                            </td>
                                            <td>{{ ucfirst(str_replace('_', ' ', $data->period_type)) }} {{ $data->period_value }}, {{ $data->financial_year }}</td>
                                            <td>{{ number_format($data->target_amount, 2) }}</td>
                                            <td>{{ number_format($data->actual_amount, 2) }}</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 80px; height: 20px;">
                                                        <div class="progress-bar bg-{{ $data->achievement_percentage >= 100 ? 'success' : ($data->achievement_percentage >= 75 ? 'warning' : 'danger') }}" 
                                                             style="width: {{ min($data->achievement_percentage, 100) }}%">
                                                        </div>
                                                    </div>
                                                    <span class="badge badge-{{ $data->achievement_percentage >= 100 ? 'success' : ($data->achievement_percentage >= 75 ? 'warning' : 'danger') }}">
                                                        {{ number_format($data->achievement_percentage, 1) }}%
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                @if($data->achievement_percentage >= 100)
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-star"></i> Excellent
                                                    </span>
                                                @elseif($data->achievement_percentage >= 90)
                                                    <span class="badge badge-info">
                                                        <i class="fas fa-thumbs-up"></i> Very Good
                                                    </span>
                                                @elseif($data->achievement_percentage >= 75)
                                                    <span class="badge badge-warning">
                                                        <i class="fas fa-check"></i> Good
                                                    </span>
                                                @else
                                                    <span class="badge badge-danger">
                                                        <i class="fas fa-arrow-up"></i> Needs Improvement
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.sales.performance.user', $data->user_id) }}" 
                                                   class="btn btn-sm btn-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @else
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h5>No Performance Data</h5>
                        <p class="text-muted">No performance data available for the selected criteria.</p>
                        <a href="{{ route('admin.sales.targets.index') }}" class="btn btn-primary">
                            Set Up Targets
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@stop

@push('css')
<style>
    .avatar-sm {
        width: 32px;
        height: 32px;
        font-size: 14px;
        font-weight: bold;
    }
    
    .progress {
        background-color: #e9ecef;
    }
</style>
@endpush
