<?php echo view_render_event('admin.contacts.persons.view.organization.before', ['person' => $person]); ?>


<?php if($person?->organization): ?>
    <div class="flex w-full flex-col gap-4 border-b border-gray-200 p-4 dark:border-gray-800">
        <h4 class="flex items-center justify-between font-semibold dark:text-white">
            <?php echo app('translator')->get('admin::app.contacts.persons.view.about-organization'); ?>

            <a
                href="<?php echo e(route('admin.contacts.organizations.edit', $person->organization->id)); ?>"
                class="icon-edit rounded-md p-1 text-2xl transition-all hover:bg-gray-100 dark:hover:bg-gray-950"
                target="_blank"
            ></a>
        </h4>

        <div class="flex gap-2">
            <?php echo view_render_event('admin.contacts.persons.view.organization.avatar.before', ['person' => $person]); ?>


            <!-- Organization Initials -->
            <?php if (isset($component)) { $__componentOriginal2d42bddad77c068ade50efea9ce906c7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2d42bddad77c068ade50efea9ce906c7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.avatar.index','data' => ['name' => $person->organization->name]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::avatar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($person->organization->name)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2d42bddad77c068ade50efea9ce906c7)): ?>
<?php $attributes = $__attributesOriginal2d42bddad77c068ade50efea9ce906c7; ?>
<?php unset($__attributesOriginal2d42bddad77c068ade50efea9ce906c7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2d42bddad77c068ade50efea9ce906c7)): ?>
<?php $component = $__componentOriginal2d42bddad77c068ade50efea9ce906c7; ?>
<?php unset($__componentOriginal2d42bddad77c068ade50efea9ce906c7); ?>
<?php endif; ?>

            <?php echo view_render_event('admin.contacts.persons.view.organization.avatar.after', ['person' => $person]); ?>


            <!-- Organization Details -->
            <div class="flex flex-col gap-1">
                <?php echo view_render_event('admin.contacts.persons.view.organization.name.before', ['person' => $person]); ?>


                <span class="font-semibold text-brandColor">
                    <?php echo e($person->organization->name); ?>

                </span>

                <?php echo view_render_event('admin.contacts.persons.view.organization.name.after', ['person' => $person]); ?>



                <?php echo view_render_event('admin.contacts.persons.view.organization.address.before', ['person' => $person]); ?>


                <?php if($person->organization->address): ?>
                    <div class="flex flex-col gap-0.5 dark:text-white">
                        <span>
                            <?php echo e($person->organization->address['address']); ?>

                        </span>
                        
                        <span>
                            <?php echo e($person->organization->address['postcode'] . '  ' . $person->organization->address['city']); ?>

                        </span>

                        <span>
                            <?php echo e(core()->state_name($person->organization->address['state'])); ?>

                        </span>

                        <span>
                            <?php echo e(core()->country_name($person->organization->address['country'])); ?>

                        </span>
                    </div>
                <?php endif; ?>

                <?php echo view_render_event('admin.contacts.persons.view.organization.address.after', ['person' => $person]); ?>

            </div>
        </div>
    </div>
<?php endif; ?>
<?php echo view_render_event('admin.contacts.persons.view.organization.after', ['person' => $person]); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/contacts/persons/view/organization.blade.php ENDPATH**/ ?>