@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.performance.user.title') }} - {{ $user->name ?? 'User' }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.performance.user.title') }}</h1>
                <p>Performance details for {{ $user->name ?? 'User' }}</p>
            </div>
            
            <div class="page-action">
                <a href="{{ route('admin.sales.performance.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Performance
                </a>
            </div>
        </div>

        <div class="page-content">
            <!-- User Info Card -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="avatar-lg bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center">
                                    {{ substr($user->name ?? 'U', 0, 2) }}
                                </div>
                                <div>
                                    <h4 class="mb-1">{{ $user->name ?? 'Unknown User' }}</h4>
                                    <p class="text-muted mb-0">{{ $user->email ?? 'No email' }}</p>
                                    <small class="text-muted">Sales Representative</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-right">
                            <div class="user-stats">
                                <div class="stat-item">
                                    <h5 class="text-primary">{{ count($targets ?? []) }}</h5>
                                    <small class="text-muted">Total Targets</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Summary -->
            @if(isset($targets) && count($targets) > 0)
                <div class="row mb-4">
                    @php
                        $totalTarget = $targets->sum('target_amount');
                        $totalActual = $targets->sum('actual_amount');
                        $avgAchievement = $targets->avg('achievement_percentage');
                        $targetsExceeded = $targets->where('achievement_percentage', '>=', 100)->count();
                    @endphp
                    
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3>{{ number_format($totalTarget, 0) }}</h3>
                                <p class="mb-0">Total Target</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3>{{ number_format($totalActual, 0) }}</h3>
                                <p class="mb-0">Total Achieved</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3>{{ number_format($avgAchievement, 1) }}%</h3>
                                <p class="mb-0">Avg Achievement</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3>{{ $targetsExceeded }}</h3>
                                <p class="mb-0">Targets Exceeded</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Targets Details -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Target Performance History</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Period</th>
                                        <th>Target Amount</th>
                                        <th>Actual Amount</th>
                                        <th>Achievement %</th>
                                        <th>Performance</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($targets as $target)
                                        <tr>
                                            <td>
                                                <strong>{{ ucfirst(str_replace('_', ' ', $target->period_type)) }} {{ $target->period_value }}</strong><br>
                                                <small class="text-muted">{{ $target->financial_year }}</small>
                                            </td>
                                            <td>{{ number_format($target->target_amount, 2) }}</td>
                                            <td>{{ number_format($target->actual_amount ?? 0, 2) }}</td>
                                            <td>
                                                @php
                                                    $percentage = $target->target_amount > 0 ? (($target->actual_amount ?? 0) / $target->target_amount) * 100 : 0;
                                                @endphp
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 100px; height: 20px;">
                                                        <div class="progress-bar bg-{{ $percentage >= 100 ? 'success' : ($percentage >= 75 ? 'warning' : 'danger') }}" 
                                                             style="width: {{ min($percentage, 100) }}%">
                                                        </div>
                                                    </div>
                                                    <span class="badge badge-{{ $percentage >= 100 ? 'success' : ($percentage >= 75 ? 'warning' : 'danger') }}">
                                                        {{ number_format($percentage, 1) }}%
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                @if($percentage >= 100)
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-star"></i> Excellent
                                                    </span>
                                                @elseif($percentage >= 90)
                                                    <span class="badge badge-info">
                                                        <i class="fas fa-thumbs-up"></i> Very Good
                                                    </span>
                                                @elseif($percentage >= 75)
                                                    <span class="badge badge-warning">
                                                        <i class="fas fa-check"></i> Good
                                                    </span>
                                                @else
                                                    <span class="badge badge-danger">
                                                        <i class="fas fa-arrow-up"></i> Needs Improvement
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($target->is_active)
                                                    <span class="badge badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-secondary">Inactive</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Performance Chart Placeholder -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Performance Trend</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h6>Performance Chart</h6>
                            <p class="text-muted">Performance trend chart will be implemented here to show target vs actual over time.</p>
                        </div>
                    </div>
                </div>
            @else
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-bullseye fa-3x text-muted mb-3"></i>
                        <h5>No Targets Set</h5>
                        <p class="text-muted">No sales targets have been set for this user yet.</p>
                        <a href="{{ route('admin.sales.targets.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Target
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@stop

@push('css')
<style>
    .avatar-lg {
        width: 64px;
        height: 64px;
        font-size: 24px;
        font-weight: bold;
    }
    
    .user-stats .stat-item {
        text-align: center;
    }
    
    .user-stats .stat-item h5 {
        font-size: 2rem;
        margin-bottom: 0.25rem;
    }
    
    .progress {
        background-color: #e9ecef;
    }
    
    .card.bg-primary,
    .card.bg-success,
    .card.bg-info,
    .card.bg-warning {
        border: none;
    }
</style>
@endpush
