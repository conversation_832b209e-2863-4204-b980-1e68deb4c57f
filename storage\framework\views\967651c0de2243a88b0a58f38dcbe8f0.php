<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    
     <?php $__env->slot('title', null, []); ?> 
        <?php echo app('translator')->get('admin::app.leads.report-title'); ?>
     <?php $__env->endSlot(); ?>

    <div class="flex flex-col gap-4">
        
        <div class="flex items-center justify-between">
            <p class="text-xl font-bold dark:text-white">
                <?php echo app('translator')->get('admin::app.leads.report-title'); ?>
            </p>

            
            <div class="flex items-center gap-x-4">
                <div class="flex items-center gap-x-2">
                    <select 
                        name="date_filter" 
                        id="date_filter"
                        class="min-w-[150px] px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-600 dark:text-gray-300 dark:bg-gray-900 dark:border-gray-800 focus:outline-none focus:ring-2 focus:ring-brandColor focus:border-transparent"
                        onchange="applyDateFilter(this.value)"
                    >
                        <option value="all" <?php echo e(request()->get('filter') == 'all' ? 'selected' : ''); ?>>All Time</option>
                        <option value="weekly" <?php echo e(request()->get('filter') == 'weekly' ? 'selected' : ''); ?>>This Week</option>
                        <option value="monthly" <?php echo e(request()->get('filter') == 'monthly' ? 'selected' : ''); ?>>This Month</option>
                        <option value="quarterly" <?php echo e(request()->get('filter') == 'quarterly' ? 'selected' : ''); ?>>This Quarter</option>
                        <option value="annually" <?php echo e(request()->get('filter') == 'annually' ? 'selected' : ''); ?>>This Year</option>
                        <option value="custom" <?php echo e(request()->get('filter') == 'custom' ? 'selected' : ''); ?>>Custom Range</option>
                    </select>

                    <div id="custom-range" class="flex items-center gap-x-2 <?php echo e(request()->get('filter') != 'custom' ? 'hidden' : ''); ?>">
                        <input 
                            type="date" 
                            id="start_date" 
                            name="start_date"
                            class="px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-600 dark:text-gray-300 dark:bg-gray-900 dark:border-gray-800 focus:outline-none focus:ring-2 focus:ring-brandColor focus:border-transparent"
                            value="<?php echo e(request()->get('start_date')); ?>"
                            max="<?php echo e(date('Y-m-d')); ?>"
                            onchange="validateDates()"
                        >
                        <span class="text-gray-500">to</span>
                        <input 
                            type="date" 
                            id="end_date" 
                            name="end_date"
                            class="px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-600 dark:text-gray-300 dark:bg-gray-900 dark:border-gray-800 focus:outline-none focus:ring-2 focus:ring-brandColor focus:border-transparent"
                            value="<?php echo e(request()->get('end_date')); ?>"
                            max="<?php echo e(date('Y-m-d')); ?>"
                            onchange="validateDates()"
                        >
                        <button 
                            onclick="applyCustomDateFilter()"
                            id="apply-date-filter"
                            class="px-4 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-gray-600 hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700"
                        >
                            Apply
                        </button>
                    </div>

                    <button 
                        onclick="resetFilter()"
                        class="min-w-[150px] px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-600 dark:text-gray-300 dark:bg-gray-900 dark:border-gray-800 focus:outline-none focus:ring-2 focus:ring-brandColor focus:border-transparent"
                        title="Reset Filter"
                    >
                        Reset
                    </button>
                </div>

                <a 
                    href="<?php echo e(route('admin.leads.report.export', array_merge(['filter' => request()->get('filter', 'all')], request()->only(['start_date', 'end_date'])))); ?>" 
                    class="primary-button"
                >
                    Export
                </a>
            </div>
        </div>

        
        <div class="relative">
            <?php if (isset($component)) { $__componentOriginal88f696f67fbaf98c7316dd2aa568a321 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal88f696f67fbaf98c7316dd2aa568a321 = $attributes; } ?>
<?php $component = App\View\Components\Report::resolve(['leads' => $leads] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('report'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Report::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal88f696f67fbaf98c7316dd2aa568a321)): ?>
<?php $attributes = $__attributesOriginal88f696f67fbaf98c7316dd2aa568a321; ?>
<?php unset($__attributesOriginal88f696f67fbaf98c7316dd2aa568a321); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal88f696f67fbaf98c7316dd2aa568a321)): ?>
<?php $component = $__componentOriginal88f696f67fbaf98c7316dd2aa568a321; ?>
<?php unset($__componentOriginal88f696f67fbaf98c7316dd2aa568a321); ?>
<?php endif; ?>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            function applyDateFilter(filter) {
                if (filter === 'custom') {
                    document.getElementById('custom-range').classList.remove('hidden');
                    return;
                }
                
                document.getElementById('custom-range').classList.add('hidden');
                window.location.href = `<?php echo e(route('admin.leads.report')); ?>?filter=${filter}`;
            }

            function validateDates() {
                const startDate = document.getElementById('start_date');
                const endDate = document.getElementById('end_date');
                const applyButton = document.getElementById('apply-date-filter');
                
                startDate.classList.remove('border-red-500');
                endDate.classList.remove('border-red-500');
                
                if (startDate.value && endDate.value) {
                    if (new Date(endDate.value) < new Date(startDate.value)) {
                        endDate.classList.add('border-red-500');
                        applyButton.disabled = true;
                        alert('End date cannot be earlier than start date');
                        return false;
                    }
                }
                
                applyButton.disabled = false;
                return true;
            }

            function applyCustomDateFilter() {
                const startDate = document.getElementById('start_date');
                const endDate = document.getElementById('end_date');

                if (!startDate.value || !endDate.value) {
                    alert('Please select both start and end dates');
                    return;
                }

                if (!validateDates()) {
                    return;
                }

                window.location.href = `<?php echo e(route('admin.leads.report')); ?>?filter=custom&start_date=${startDate.value}&end_date=${endDate.value}`;
            }

            function resetFilter() {
                // Reset the date filter dropdown
                document.getElementById('date_filter').value = 'all';
                
                // Reset and hide the custom date range fields
                document.getElementById('start_date').value = '';
                document.getElementById('end_date').value = '';
                document.getElementById('custom-range').classList.add('hidden');
                
                // Redirect to the report page without any filters
                window.location.href = '<?php echo e(route('admin.leads.report')); ?>';
            }

            // Set initial state
            if (document.getElementById('date_filter').value === 'custom') {
                document.getElementById('custom-range').classList.remove('hidden');
            }
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/leads/report.blade.php ENDPATH**/ ?>