<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\View\Component;

class Report extends Component
{
    /**
     * The leads data collection or paginator.
     *
     * @var Collection|LengthAwarePaginator
     */
    public $leads;

    /**
     * Create a new component instance.
     *
     * @param  Collection|LengthAwarePaginator  $leads
     * @return void
     */
    public function __construct($leads)
    {
        $this->leads = $leads;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.report');
    }
}
