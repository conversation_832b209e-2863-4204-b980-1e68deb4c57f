<?php

namespace Webkul\Sales\Repositories;

use Carbon\Carbon;
use Webkul\Core\Eloquent\Repository;
use Webkul\Sales\Contracts\SalesTarget;

class SalesTargetRepository extends Repository
{
    /**
     * Specify model class name.
     */
    public function model(): string
    {
        return SalesTarget::class;
    }

    /**
     * Create a new sales target.
     */
    public function create(array $data): SalesTarget
    {
        // Set created_by if not provided
        if (! isset($data['created_by'])) {
            $data['created_by'] = auth()->guard('user')->id();
        }

        // Calculate start and end dates if not a custom period
        if ($data['period_type'] !== 'custom') {
            $dates = $this->calculatePeriodDates($data);
            $data = array_merge($data, $dates);
        }

        return parent::create($data);
    }

    /**
     * Update sales target.
     */
    public function update(array $data, $id): SalesTarget
    {
        // Set updated_by
        $data['updated_by'] = auth()->guard('user')->id();

        // Recalculate dates if period information changed and not custom
        if (($data['period_type'] ?? 'custom') !== 'custom' &&
            (isset($data['financial_year']) || isset($data['period_type']) ||
             isset($data['quarter']) || isset($data['month']) || isset($data['half_year']))) {
            $dates = $this->calculatePeriodDates($data);
            $data = array_merge($data, $dates);
        }

        return parent::update($data, $id);
    }

    /**
     * Calculate start and end dates based on period type.
     */
    protected function calculatePeriodDates(array $data): array
    {
        $financialYear = $data['financial_year'];
        $periodType = $data['period_type'];

        // Extract start year from financial year (e.g., '2024-25' -> 2024)
        $startYear = (int) substr($financialYear, 0, 4);

        switch ($periodType) {
            case 'half_yearly':
                return $this->getHalfYearlyDates($startYear, $data['half_year']);
            case 'quarterly':
                return $this->getQuarterlyDates($startYear, $data['quarter']);
            case 'monthly':
                return $this->getMonthlyDates($startYear, $data['month']);
            default: // annual
                return [
                    'start_date' => Carbon::create($startYear, 4, 1)->format('Y-m-d'),
                    'end_date'   => Carbon::create($startYear + 1, 3, 31)->format('Y-m-d'),
                ];
        }
    }

    /**
     * Get half-yearly date range.
     */
    protected function getHalfYearlyDates(int $startYear, int $half): array
    {
        if ($half == 1) {
            // First half: April - September
            return [
                'start_date' => Carbon::create($startYear, 4, 1)->format('Y-m-d'),
                'end_date'   => Carbon::create($startYear, 9, 30)->format('Y-m-d'),
            ];
        } else {
            // Second half: October - March
            return [
                'start_date' => Carbon::create($startYear, 10, 1)->format('Y-m-d'),
                'end_date'   => Carbon::create($startYear + 1, 3, 31)->format('Y-m-d'),
            ];
        }
    }

    /**
     * Get quarterly date range.
     */
    protected function getQuarterlyDates(int $startYear, int $quarter): array
    {
        $quarterMonths = [
            1 => [4, 6],   // Q1: Apr-Jun
            2 => [7, 9],   // Q2: Jul-Sep
            3 => [10, 12], // Q3: Oct-Dec
            4 => [1, 3],   // Q4: Jan-Mar (next year)
        ];

        $months = $quarterMonths[$quarter];
        $year = $quarter == 4 ? $startYear + 1 : $startYear;

        return [
            'start_date' => Carbon::create($year, $months[0], 1)->format('Y-m-d'),
            'end_date'   => Carbon::create($year, $months[1])->endOfMonth()->format('Y-m-d'),
        ];
    }

    /**
     * Get monthly date range.
     */
    protected function getMonthlyDates(int $startYear, int $month): array
    {
        // Months 1-3 are in the next year for financial year
        $year = $month <= 3 ? $startYear + 1 : $startYear;

        return [
            'start_date' => Carbon::create($year, $month, 1)->format('Y-m-d'),
            'end_date'   => Carbon::create($year, $month)->endOfMonth()->format('Y-m-d'),
        ];
    }

    /**
     * Get targets for a specific user and financial year.
     */
    public function getUserTargets(int $userId, ?string $financialYear = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = $this->model->where('user_id', $userId);

        if ($financialYear) {
            $query->where('financial_year', $financialYear);
        }

        return $query->with(['achievements', 'user'])->get();
    }

    /**
     * Get team targets.
     */
    public function getTeamTargets(array $userIds, ?string $financialYear = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = $this->model->whereIn('user_id', $userIds);

        if ($financialYear) {
            $query->where('financial_year', $financialYear);
        }

        return $query->with(['achievements', 'user'])->get();
    }

    /**
     * Get performance summary for date range.
     */
    public function getPerformanceSummary(array $filters = []): \Illuminate\Database\Eloquent\Collection
    {
        $query = $this->model->query();

        // Apply filters
        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['financial_year'])) {
            $query->where('financial_year', $filters['financial_year']);
        }

        if (isset($filters['period_type'])) {
            $query->where('period_type', $filters['period_type']);
        }

        if (isset($filters['target_type'])) {
            $query->where('target_type', $filters['target_type']);
        }

        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('start_date', [$filters['start_date'], $filters['end_date']]);
        }

        return $query->with('user')->get();
    }
}
