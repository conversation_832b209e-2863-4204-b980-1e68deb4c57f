<x-admin::layouts>
    <x-slot:title>
        @lang('sales::app.reports.target-vs-actual-report')
    </x-slot>

    <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="flex cursor-pointer items-center">
                    <x-admin::breadcrumbs name="sales.reports.target-vs-actual" />
                </div>

                <div class="text-xl font-bold dark:text-white">
                    Target vs Actual Report
                </div>

                <div class="text-gray-600 dark:text-gray-300">
                    Compare sales targets with actual achievements across different time periods
                </div>
            </div>

            <div class="flex items-center gap-x-2.5">
                <a href="{{ route('admin.sales.reports.index') }}" class="secondary-button">
                    <span class="icon-arrow-left text-2xl"></span>
                    Back to Reports
                </a>
            </div>
        </div>

        <!-- Filters -->
        <x-admin::layouts.filter>
            <x-slot:form>
                <form method="GET" action="{{ route('admin.sales.reports.target_vs_actual') }}">
                    <div class="flex gap-[16px] items-center">
                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                Financial Year
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="financial_year"
                                :value="request('financial_year')"
                            >
                                <option value="">All Years</option>
                                @for($year = date('Y'); $year >= date('Y') - 5; $year--)
                                    <option value="{{ $year }}" {{ ($filters['financial_year'] ?? '') == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endfor
                            </x-admin::form.control-group.control>
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                Period Type
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="period_type"
                                :value="request('period_type')"
                            >
                                <option value="">All Periods</option>
                                <option value="quarterly" {{ ($filters['period_type'] ?? '') == 'quarterly' ? 'selected' : '' }}>Quarterly</option>
                                <option value="monthly" {{ ($filters['period_type'] ?? '') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                <option value="half_yearly" {{ ($filters['period_type'] ?? '') == 'half_yearly' ? 'selected' : '' }}>Half Yearly</option>
                            </x-admin::form.control-group.control>
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                Sales Representative
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="user_id"
                                :value="request('user_id')"
                            >
                                <option value="">All Users</option>
                                @if(isset($users))
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ ($filters['user_id'] ?? '') == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }}
                                        </option>
                                    @endforeach
                                @endif
                            </x-admin::form.control-group.control>
                        </x-admin::form.control-group>

                        <div class="flex gap-[16px] items-center">
                            <button type="submit" class="primary-button">
                                Apply Filters
                            </button>

                            <a href="{{ route('admin.sales.reports.target_vs_actual') }}" class="secondary-button">
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </x-slot>
        </x-admin::layouts.filter>

        <!-- Summary Cards -->
        @if(isset($reportData) && count($reportData) > 0)
            @php
                $totalTarget = $reportData->sum('target_value');
                $totalAchieved = $reportData->sum('achieved_value');
                $avgAchievement = $reportData->avg('achievement_percentage');
                $targetsAchieved = $reportData->where('achievement_percentage', '>=', 100)->count();
            @endphp

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center">
                        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                            <span class="icon-target text-xl text-blue-600 dark:text-blue-400"></span>
                        </div>
                        <div class="ml-4">
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">${{ number_format($totalTarget, 0) }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Total Target</p>
                        </div>
                    </div>
                </div>

                <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center">
                        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
                            <span class="icon-stats-up text-xl text-green-600 dark:text-green-400"></span>
                        </div>
                        <div class="ml-4">
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">${{ number_format($totalAchieved, 0) }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Total Actual</p>
                        </div>
                    </div>
                </div>

                <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center">
                        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900">
                            <span class="icon-percentage text-xl text-purple-600 dark:text-purple-400"></span>
                        </div>
                        <div class="ml-4">
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($avgAchievement, 1) }}%</p>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Avg Achievement</p>
                        </div>
                    </div>
                </div>

                <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center">
                        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900">
                            <span class="icon-trophy text-xl text-orange-600 dark:text-orange-400"></span>
                        </div>
                        <div class="ml-4">
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $targetsAchieved }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Targets Met</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900">
                <div class="border-b border-gray-200 px-6 py-4 dark:border-gray-800">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performance Details</h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
                        <thead class="bg-gray-50 dark:bg-gray-800">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Sales Rep
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Period
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Target Amount
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Actual Amount
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Achievement %
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Status
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
                            @foreach($reportData as $data)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                    <td class="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $data->user->name ?? 'N/A' }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                                        {{ ucfirst($data->period_type) }}
                                        @if($data->quarter) Q{{ $data->quarter }} @endif
                                        @if($data->month) {{ date('M', mktime(0, 0, 0, $data->month, 1)) }} @endif
                                        {{ $data->financial_year }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                                        ${{ number_format($data->target_value, 2) }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                                        ${{ number_format($data->achieved_value, 2) }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-sm">
                                        <span class="inline-flex rounded-full px-2 text-xs font-semibold leading-5
                                            {{ $data->achievement_percentage >= 100 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                               ($data->achievement_percentage >= 75 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                                'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200') }}">
                                            {{ number_format($data->achievement_percentage, 1) }}%
                                        </span>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-sm">
                                        @if($data->achievement_percentage >= 100)
                                            <span class="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                Target Met
                                            </span>
                                        @elseif($data->achievement_percentage >= 75)
                                            <span class="inline-flex rounded-full bg-yellow-100 px-2 text-xs font-semibold leading-5 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                Close
                                            </span>
                                        @else
                                            <span class="inline-flex rounded-full bg-red-100 px-2 text-xs font-semibold leading-5 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                Below Target
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @else
            <div class="rounded-lg border border-gray-200 bg-white p-8 text-center dark:border-gray-800 dark:bg-gray-900">
                <span class="icon-chart text-6xl text-gray-300 dark:text-gray-600"></span>
                <h3 class="mt-4 text-lg font-semibold text-gray-900 dark:text-white">No Data Available</h3>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
                    No performance data found for the selected criteria.
                </p>
                <div class="mt-6">
                    <a href="{{ route('admin.sales.targets.index') }}" class="primary-button">
                        Set Up Targets
                    </a>
                </div>
            </div>
        @endif
    </div>
</x-admin::layouts>
