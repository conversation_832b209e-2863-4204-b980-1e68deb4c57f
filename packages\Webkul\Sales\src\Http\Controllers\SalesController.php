<?php

namespace Webkul\Sales\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Sales\Repositories\SalesTargetRepository;
use Webkul\User\Repositories\UserRepository;

class SalesController extends Controller
{
    public function __construct(
        protected SalesTargetRepository $salesTargetRepository,
        protected UserRepository $userRepository
    ) {}

    /**
     * Display the sales dashboard.
     */
    public function index(Request $request): View
    {
        $currentFinancialYear = $this->getCurrentFinancialYear();
        $selectedYear = $request->get('financial_year', $currentFinancialYear);

        // Get dashboard statistics
        $stats = $this->getDashboardStats($selectedYear);

        // Get top performers
        $topPerformers = $this->getTopPerformers($selectedYear);

        // Get recent achievements
        $recentAchievements = $this->getRecentAchievements();

        // Get performance trends
        $performanceTrends = $this->getPerformanceTrends($selectedYear);

        // Get available financial years
        $financialYears = $this->salesTargetRepository
            ->distinct()
            ->pluck('financial_year')
            ->sort()
            ->values();

        return view('sales::dashboard.index', compact(
            'stats',
            'topPerformers',
            'recentAchievements',
            'performanceTrends',
            'financialYears',
            'selectedYear'
        ));
    }

    /**
     * Get current financial year.
     */
    protected function getCurrentFinancialYear(): string
    {
        $now = Carbon::now();
        $year = $now->year;

        // Financial year starts from April
        if ($now->month < 4) {
            $year--;
        }

        return $year.'-'.substr($year + 1, 2);
    }

    /**
     * Get dashboard statistics.
     */
    protected function getDashboardStats(string $financialYear): array
    {
        $targets = $this->salesTargetRepository->findWhere([
            'financial_year' => $financialYear,
            'status'         => 'active',
        ]);

        $totalTargets = $targets->count();
        $totalTargetValue = $targets->sum('target_value');
        $totalAchievedValue = $targets->sum('achieved_value');
        $avgAchievementPercentage = $targets->avg('achievement_percentage') ?? 0;
        $targetsAchieved = $targets->where('achievement_percentage', '>=', 100)->count();

        return [
            'total_targets'              => $totalTargets,
            'total_target_value'         => $totalTargetValue,
            'total_achieved_value'       => $totalAchievedValue,
            'avg_achievement_percentage' => round($avgAchievementPercentage, 2),
            'targets_achieved'           => $targetsAchieved,
            'achievement_rate'           => $totalTargets > 0 ? round(($targetsAchieved / $totalTargets) * 100, 2) : 0,
        ];
    }

    /**
     * Get top performers.
     */
    protected function getTopPerformers(string $financialYear, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return $this->salesTargetRepository
            ->with('user')
            ->where('financial_year', $financialYear)
            ->where('status', 'active')
            ->orderBy('achievement_percentage', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent achievements.
     */
    protected function getRecentAchievements(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return \Webkul\Sales\Models\SalesAchievement::with(['user', 'salesTarget'])
            ->orderBy('achievement_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get performance trends for charts.
     */
    protected function getPerformanceTrends(string $financialYear): array
    {
        // Get monthly performance data
        $monthlyData = $this->salesTargetRepository
            ->where('financial_year', $financialYear)
            ->where('period_type', 'monthly')
            ->orderBy('month')
            ->get()
            ->groupBy('month')
            ->map(function ($targets) {
                return [
                    'target'     => $targets->sum('target_value'),
                    'achieved'   => $targets->sum('achieved_value'),
                    'percentage' => $targets->avg('achievement_percentage') ?? 0,
                ];
            });

        // Get quarterly performance data
        $quarterlyData = $this->salesTargetRepository
            ->where('financial_year', $financialYear)
            ->where('period_type', 'quarterly')
            ->orderBy('quarter')
            ->get()
            ->groupBy('quarter')
            ->map(function ($targets) {
                return [
                    'target'     => $targets->sum('target_value'),
                    'achieved'   => $targets->sum('achieved_value'),
                    'percentage' => $targets->avg('achievement_percentage') ?? 0,
                ];
            });

        return [
            'monthly'   => $monthlyData,
            'quarterly' => $quarterlyData,
        ];
    }
}
