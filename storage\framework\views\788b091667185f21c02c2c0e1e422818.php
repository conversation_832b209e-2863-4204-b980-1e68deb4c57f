<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        <?php echo app('translator')->get('admin::app.contacts.persons.view.title', ['name' => $person->name]); ?>
     <?php $__env->endSlot(); ?>

    <!-- Content -->
    <div class="flex gap-4">
        <!-- Left Panel -->
        <?php echo view_render_event('admin.contact.persons.view.left.before', ['person' => $person]); ?>


        <div class="sticky top-[73px] flex min-w-[394px] max-w-[394px] flex-col self-start rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900">
            <!-- Person Information -->
            <div class="flex w-full flex-col gap-2 border-b border-gray-200 p-4 dark:border-gray-800">
                <!-- Breadcrums -->
                <div class="flex items-center justify-between">
                    <?php if (isset($component)) { $__componentOriginal477735b45b070062c5df1d72c43d48f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal477735b45b070062c5df1d72c43d48f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.breadcrumbs.index','data' => ['name' => 'contacts.persons.view','entity' => $person]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'contacts.persons.view','entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($person)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $attributes = $__attributesOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__attributesOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $component = $__componentOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__componentOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
                </div>

                <?php echo view_render_event('admin.contact.persons.view.tags.before', ['person' => $person]); ?>


                <!-- Tags -->
                <?php if (isset($component)) { $__componentOriginalf851be63606bb172aaceed482091e22c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf851be63606bb172aaceed482091e22c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.tags.index','data' => ['attachEndpoint' => route('admin.contacts.persons.tags.attach', $person->id),'detachEndpoint' => route('admin.contacts.persons.tags.detach', $person->id),'addedTags' => $person->tags]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::tags'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['attach-endpoint' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.contacts.persons.tags.attach', $person->id)),'detach-endpoint' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.contacts.persons.tags.detach', $person->id)),'added-tags' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($person->tags)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf851be63606bb172aaceed482091e22c)): ?>
<?php $attributes = $__attributesOriginalf851be63606bb172aaceed482091e22c; ?>
<?php unset($__attributesOriginalf851be63606bb172aaceed482091e22c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf851be63606bb172aaceed482091e22c)): ?>
<?php $component = $__componentOriginalf851be63606bb172aaceed482091e22c; ?>
<?php unset($__componentOriginalf851be63606bb172aaceed482091e22c); ?>
<?php endif; ?>

                <?php echo view_render_event('admin.contact.persons.view.tags.after', ['person' => $person]); ?>


                
                <!-- Title -->
                <div class="mb-4 flex flex-col gap-0.5">
                    <?php echo view_render_event('admin.contact.persons.view.title.before', ['person' => $person]); ?>


                    <h3 class="text-lg font-bold dark:text-white">
                        <?php echo e($person->name); ?>

                    </h3>

                    <p class="dark:text-white">
                        <?php echo e($person->job_title); ?>

                    </p>

                    <?php echo view_render_event('admin.contact.persons.view.title.after', ['person' => $person]); ?>

                </div>
                
                <!-- Activity Actions -->
                <div class="flex flex-wrap gap-2">
                    <?php echo view_render_event('admin.contact.persons.view.actions.before', ['person' => $person]); ?>


                    <!-- Mail Activity Action -->
                    <?php if (isset($component)) { $__componentOriginal6e7e62b7e8fed3be26d9a026b4495e9a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6e7e62b7e8fed3be26d9a026b4495e9a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.actions.mail','data' => ['entity' => $person,'entityControlName' => 'person_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities.actions.mail'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($person),'entity-control-name' => 'person_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6e7e62b7e8fed3be26d9a026b4495e9a)): ?>
<?php $attributes = $__attributesOriginal6e7e62b7e8fed3be26d9a026b4495e9a; ?>
<?php unset($__attributesOriginal6e7e62b7e8fed3be26d9a026b4495e9a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6e7e62b7e8fed3be26d9a026b4495e9a)): ?>
<?php $component = $__componentOriginal6e7e62b7e8fed3be26d9a026b4495e9a; ?>
<?php unset($__componentOriginal6e7e62b7e8fed3be26d9a026b4495e9a); ?>
<?php endif; ?>

                    <!-- File Activity Action -->
                    <?php if (isset($component)) { $__componentOriginale7ed32b182ae3e00a04d1065a8a2ff86 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale7ed32b182ae3e00a04d1065a8a2ff86 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.actions.file','data' => ['entity' => $person,'entityControlName' => 'person_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities.actions.file'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($person),'entity-control-name' => 'person_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale7ed32b182ae3e00a04d1065a8a2ff86)): ?>
<?php $attributes = $__attributesOriginale7ed32b182ae3e00a04d1065a8a2ff86; ?>
<?php unset($__attributesOriginale7ed32b182ae3e00a04d1065a8a2ff86); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale7ed32b182ae3e00a04d1065a8a2ff86)): ?>
<?php $component = $__componentOriginale7ed32b182ae3e00a04d1065a8a2ff86; ?>
<?php unset($__componentOriginale7ed32b182ae3e00a04d1065a8a2ff86); ?>
<?php endif; ?>

                    <!-- Note Activity Action -->
                    <?php if (isset($component)) { $__componentOriginal8bdd483ce4bdd8186f2e725c84d6fd85 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8bdd483ce4bdd8186f2e725c84d6fd85 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.actions.note','data' => ['entity' => $person,'entityControlName' => 'person_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities.actions.note'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($person),'entity-control-name' => 'person_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8bdd483ce4bdd8186f2e725c84d6fd85)): ?>
<?php $attributes = $__attributesOriginal8bdd483ce4bdd8186f2e725c84d6fd85; ?>
<?php unset($__attributesOriginal8bdd483ce4bdd8186f2e725c84d6fd85); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8bdd483ce4bdd8186f2e725c84d6fd85)): ?>
<?php $component = $__componentOriginal8bdd483ce4bdd8186f2e725c84d6fd85; ?>
<?php unset($__componentOriginal8bdd483ce4bdd8186f2e725c84d6fd85); ?>
<?php endif; ?>

                    <!-- Activity Action -->
                    <?php if (isset($component)) { $__componentOriginalc39954a677175d0d994f44af8c16faaf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc39954a677175d0d994f44af8c16faaf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.actions.activity','data' => ['entity' => $person,'entityControlName' => 'person_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities.actions.activity'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($person),'entity-control-name' => 'person_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc39954a677175d0d994f44af8c16faaf)): ?>
<?php $attributes = $__attributesOriginalc39954a677175d0d994f44af8c16faaf; ?>
<?php unset($__attributesOriginalc39954a677175d0d994f44af8c16faaf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc39954a677175d0d994f44af8c16faaf)): ?>
<?php $component = $__componentOriginalc39954a677175d0d994f44af8c16faaf; ?>
<?php unset($__componentOriginalc39954a677175d0d994f44af8c16faaf); ?>
<?php endif; ?>

                    <?php echo view_render_event('admin.contact.persons.view.actions.after', ['person' => $person]); ?>

                </div>
            </div>

            <!-- Person Attributes -->
            <?php echo $__env->make('admin::contacts.persons.view.attributes', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <!-- Contact Organization -->
            <?php echo $__env->make('admin::contacts.persons.view.organization', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>

        <?php echo view_render_event('admin.contact.persons.view.left.after', ['person' => $person]); ?>


        <!-- Right Panel -->
        <div class="flex w-full flex-col gap-4 rounded-lg">
            <?php echo view_render_event('admin.contact.persons.view.right.before', ['person' => $person]); ?>


            <!-- Stages Navigation -->
            <?php if (isset($component)) { $__componentOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.index','data' => ['endpoint' => route('admin.contacts.persons.activities.index', $person->id)]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['endpoint' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.contacts.persons.activities.index', $person->id))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173)): ?>
<?php $attributes = $__attributesOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173; ?>
<?php unset($__attributesOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173)): ?>
<?php $component = $__componentOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173; ?>
<?php unset($__componentOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173); ?>
<?php endif; ?>

            <?php echo view_render_event('admin.contact.persons.view.right.after', ['person' => $person]); ?>

        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/contacts/persons/view.blade.php ENDPATH**/ ?>