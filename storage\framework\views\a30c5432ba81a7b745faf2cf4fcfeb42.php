<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Page Title -->
     <?php $__env->slot('title', null, []); ?> 
        <?php echo app('translator')->get('admin::app.leads.bulk-upload.title'); ?>
     <?php $__env->endSlot(); ?>

    <div class="flex gap-4 justify-between items-center max-sm:flex-wrap">
        <p class="text-xl text-gray-800 dark:text-white font-bold">
            <?php echo app('translator')->get('admin::app.leads.bulk-upload.title'); ?>
        </p>

        <div class="flex gap-x-2.5 items-center">
            <a 
                href="<?php echo e(route('admin.leads.bulk_upload.download_sample')); ?>" 
                class="primary-button"
            >
                <?php echo app('translator')->get('admin::app.leads.bulk-upload.download-sample'); ?>
            </a>
        </div>
    </div>

    <div class="flex gap-2.5 mt-3.5 max-xl:flex-wrap">
        <!-- Left Component -->
        <div class="flex flex-col gap-2 flex-1 max-xl:flex-auto">
            <div class="p-4 bg-white dark:bg-gray-900 rounded box-shadow">
                <p class="text-base text-gray-800 dark:text-white font-semibold mb-4">
                    <?php echo app('translator')->get('admin::app.leads.bulk-upload.instructions'); ?>
                </p>

                <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2">
                    <li><?php echo app('translator')->get('admin::app.leads.bulk-upload.instruction-1'); ?></li>
                    <li><?php echo app('translator')->get('admin::app.leads.bulk-upload.instruction-2'); ?></li>
                    <li><?php echo app('translator')->get('admin::app.leads.bulk-upload.instruction-3'); ?></li>
                    <li><?php echo app('translator')->get('admin::app.leads.bulk-upload.instruction-4'); ?></li>
                    <li><?php echo app('translator')->get('admin::app.leads.bulk-upload.instruction-5'); ?></li>
                </ul>
            </div>

            <div class="p-4 bg-white dark:bg-gray-900 rounded box-shadow">
                <p class="text-base text-gray-800 dark:text-white font-semibold mb-4">
                    <?php echo app('translator')->get('admin::app.leads.bulk-upload.column-description'); ?>
                </p>

                <div class="grid grid-cols-1 gap-4">
                    <div class="border-b pb-2">
                        <p class="font-medium">title <span class="text-red-500">*</span></p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.title-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">description</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.description-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">lead_value</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.lead-value-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">status</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.status-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">expected_close_date</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.expected-close-date-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">person_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.person-name-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">user_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.user-name-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">source_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.source-name-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">type_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.type-name-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">pipeline_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.pipeline-name-desc'); ?></p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">stage_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo app('translator')->get('admin::app.leads.bulk-upload.stage-name-desc'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Component -->
        <div class="flex-1 max-xl:flex-auto">
            <div class="bg-white dark:bg-gray-900 rounded box-shadow">
                <p class="p-4 text-base text-gray-800 dark:text-white font-semibold">
                    <?php echo app('translator')->get('admin::app.leads.bulk-upload.upload-file'); ?>
                </p>

                <div class="p-4 border-t dark:border-gray-800">
                    <?php if (isset($component)) { $__componentOriginal81b4d293d9113446bb908fc8aef5c8f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.index','data' => ['action' => route('admin.leads.bulk_upload.upload'),'method' => 'POST','enctype' => 'multipart/form-data']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.leads.bulk_upload.upload')),'method' => 'POST','enctype' => 'multipart/form-data']); ?>
                        <div class="mb-4">
                            <?php if (isset($component)) { $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                                <?php if (isset($component)) { $__componentOriginal8378211f70f8c39b16d47cecdac9c7c8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8378211f70f8c39b16d47cecdac9c7c8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.label','data' => ['class' => 'required']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.label'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'required']); ?>
                                    <?php echo app('translator')->get('admin::app.leads.bulk-upload.file'); ?>
                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8378211f70f8c39b16d47cecdac9c7c8)): ?>
<?php $attributes = $__attributesOriginal8378211f70f8c39b16d47cecdac9c7c8; ?>
<?php unset($__attributesOriginal8378211f70f8c39b16d47cecdac9c7c8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8378211f70f8c39b16d47cecdac9c7c8)): ?>
<?php $component = $__componentOriginal8378211f70f8c39b16d47cecdac9c7c8; ?>
<?php unset($__componentOriginal8378211f70f8c39b16d47cecdac9c7c8); ?>
<?php endif; ?>

                                <?php if (isset($component)) { $__componentOriginal53af403f6b2179a3039d488b8ab2a267 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53af403f6b2179a3039d488b8ab2a267 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.control','data' => ['type' => 'file','name' => 'file','rules' => 'required','label' => trans('admin::app.leads.bulk-upload.file'),'accept' => '.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.control'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'file','name' => 'file','rules' => 'required','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.bulk-upload.file')),'accept' => '.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $attributes = $__attributesOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $component = $__componentOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__componentOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>

                                <?php if (isset($component)) { $__componentOriginal8da25fb6534e2ef288914e35c32417f8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8da25fb6534e2ef288914e35c32417f8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.error','data' => ['controlName' => 'file']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['control-name' => 'file']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8da25fb6534e2ef288914e35c32417f8)): ?>
<?php $attributes = $__attributesOriginal8da25fb6534e2ef288914e35c32417f8; ?>
<?php unset($__attributesOriginal8da25fb6534e2ef288914e35c32417f8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8da25fb6534e2ef288914e35c32417f8)): ?>
<?php $component = $__componentOriginal8da25fb6534e2ef288914e35c32417f8; ?>
<?php unset($__componentOriginal8da25fb6534e2ef288914e35c32417f8); ?>
<?php endif; ?>
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $attributes = $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $component = $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
                        </div>

                        <div class="flex justify-end">
                            <button 
                                type="submit"
                                class="primary-button"
                            >
                                <?php echo app('translator')->get('admin::app.leads.bulk-upload.upload'); ?>
                            </button>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6)): ?>
<?php $attributes = $__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6; ?>
<?php unset($__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal81b4d293d9113446bb908fc8aef5c8f6)): ?>
<?php $component = $__componentOriginal81b4d293d9113446bb908fc8aef5c8f6; ?>
<?php unset($__componentOriginal81b4d293d9113446bb908fc8aef5c8f6); ?>
<?php endif; ?>
                </div>

                <?php if(session()->has('import_errors')): ?>
                    <div class="p-4 border-t dark:border-gray-800">
                        <p class="text-base text-red-600 font-semibold mb-2">
                            <?php echo app('translator')->get('admin::app.leads.bulk-upload.errors'); ?>
                        </p>

                        <div class="max-h-80 overflow-y-auto">
                            <ul class="list-disc list-inside text-red-600 space-y-1">
                                <?php $__currentLoopData = session('import_errors'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/leads/bulk-upload/index.blade.php ENDPATH**/ ?>