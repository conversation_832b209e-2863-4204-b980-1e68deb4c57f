<?php

namespace Webkul\Sales\Database\Seeders;

use Illuminate\Database\Seeder;
use Webkul\Sales\Models\SalesTarget;
use Webkul\Sales\Models\SalesAchievement;
use Webkul\Sales\Models\SalesTeam;
use Webkul\Sales\Models\SalesTeamMember;
use Webkul\User\Models\User;
use Carbon\Carbon;

class SalesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the first user (admin) to use as creator
        $admin = User::first();
        
        if (!$admin) {
            echo "No users found. Please create users first.\n";
            return;
        }

        // Get all users for targets
        $users = User::all();
        
        if ($users->count() < 2) {
            echo "Need at least 2 users to create sample data.\n";
            return;
        }

        $currentYear = Carbon::now()->year;
        $financialYear = $currentYear . '-' . substr($currentYear + 1, 2);

        // Create Sales Team
        $salesTeam = SalesTeam::create([
            'name' => 'Sales Team Alpha',
            'description' => 'Primary sales team for revenue generation',
            'team_lead_id' => $admin->id,
            'status' => 'active',
            'regions' => ['North', 'Central'],
            'created_by' => $admin->id,
        ]);

        // Add team members
        foreach ($users->take(3) as $index => $user) {
            SalesTeamMember::create([
                'sales_team_id' => $salesTeam->id,
                'user_id' => $user->id,
                'role' => $index === 0 ? 'lead' : 'member',
                'joined_date' => Carbon::now()->subMonths(rand(1, 12)),
                'status' => 'active',
            ]);
        }

        // Create Sales Targets for each user
        foreach ($users->take(5) as $user) {
            // Annual Revenue Target
            $annualTarget = SalesTarget::create([
                'user_id' => $user->id,
                'financial_year' => $financialYear,
                'period_type' => 'annual',
                'target_type' => 'revenue',
                'target_value' => rand(500000, 2000000), // 500K to 2M
                'start_date' => Carbon::create($currentYear, 4, 1),
                'end_date' => Carbon::create($currentYear + 1, 3, 31),
                'status' => 'active',
                'notes' => 'Annual revenue target for ' . $user->name,
                'created_by' => $admin->id,
            ]);

            // Quarterly Targets
            for ($quarter = 1; $quarter <= 4; $quarter++) {
                $quarterlyTarget = SalesTarget::create([
                    'user_id' => $user->id,
                    'financial_year' => $financialYear,
                    'period_type' => 'quarterly',
                    'quarter' => $quarter,
                    'target_type' => 'revenue',
                    'target_value' => $annualTarget->target_value / 4,
                    'start_date' => $this->getQuarterStartDate($currentYear, $quarter),
                    'end_date' => $this->getQuarterEndDate($currentYear, $quarter),
                    'status' => 'active',
                    'notes' => "Q{$quarter} revenue target for " . $user->name,
                    'created_by' => $admin->id,
                ]);

                // Create some achievements for completed quarters
                if ($quarter <= 2) { // Assume Q1 and Q2 are completed
                    $achievementValue = rand(
                        (int)($quarterlyTarget->target_value * 0.7), 
                        (int)($quarterlyTarget->target_value * 1.3)
                    );

                    SalesAchievement::create([
                        'user_id' => $user->id,
                        'sales_target_id' => $quarterlyTarget->id,
                        'achievement_date' => $this->getQuarterEndDate($currentYear, $quarter),
                        'achievement_type' => 'revenue',
                        'achievement_value' => $achievementValue,
                        'source_type' => 'manual',
                        'description' => "Q{$quarter} revenue achievement for " . $user->name,
                    ]);

                    // Update target with achievement
                    $quarterlyTarget->update([
                        'achieved_value' => $achievementValue,
                        'achievement_percentage' => ($achievementValue / $quarterlyTarget->target_value) * 100,
                    ]);
                }
            }

            // Monthly Lead Targets
            for ($month = 4; $month <= 12; $month++) { // Financial year months
                SalesTarget::create([
                    'user_id' => $user->id,
                    'financial_year' => $financialYear,
                    'period_type' => 'monthly',
                    'month' => $month,
                    'target_type' => 'leads',
                    'target_value' => rand(20, 100), // 20 to 100 leads per month
                    'start_date' => Carbon::create($currentYear, $month, 1),
                    'end_date' => Carbon::create($currentYear, $month)->endOfMonth(),
                    'status' => 'active',
                    'notes' => "Monthly lead target for " . $user->name,
                    'created_by' => $admin->id,
                ]);
            }
        }

        echo "Sales sample data created successfully!\n";
        echo "- Created 1 sales team with " . $users->take(3)->count() . " members\n";
        echo "- Created targets for " . $users->take(5)->count() . " users\n";
        echo "- Created achievements for Q1 and Q2\n";
    }

    private function getQuarterStartDate($year, $quarter)
    {
        $quarterMonths = [
            1 => [4, 1],   // Q1: Apr
            2 => [7, 1],   // Q2: Jul
            3 => [10, 1],  // Q3: Oct
            4 => [1, 1],   // Q4: Jan (next year)
        ];

        $month = $quarterMonths[$quarter][0];
        $day = $quarterMonths[$quarter][1];
        $targetYear = $quarter == 4 ? $year + 1 : $year;

        return Carbon::create($targetYear, $month, $day);
    }

    private function getQuarterEndDate($year, $quarter)
    {
        $quarterMonths = [
            1 => [6, 30],  // Q1: Jun 30
            2 => [9, 30],  // Q2: Sep 30
            3 => [12, 31], // Q3: Dec 31
            4 => [3, 31],  // Q4: Mar 31 (next year)
        ];

        $month = $quarterMonths[$quarter][0];
        $day = $quarterMonths[$quarter][1];
        $targetYear = $quarter == 4 ? $year + 1 : $year;

        return Carbon::create($targetYear, $month, $day);
    }
}
