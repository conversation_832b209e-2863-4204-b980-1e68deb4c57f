<?php

namespace Webkul\Sales\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Webkul\User\Models\User;
use Webkul\Sales\Contracts\SalesTeamMember as SalesTeamMemberContract;

class SalesTeamMember extends Model implements SalesTeamMemberContract
{
    protected $table = 'crm_sales_team_members';

    protected $fillable = [
        'sales_team_id',
        'user_id',
        'role',
        'joined_date',
        'left_date',
        'status',
    ];

    protected $casts = [
        'joined_date' => 'date',
        'left_date' => 'date',
    ];

    /**
     * Get the sales team.
     */
    public function salesTeam(): BelongsTo
    {
        return $this->belongsTo(SalesTeam::class);
    }

    /**
     * Get the user.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for active members.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for specific role.
     */
    public function scopeForRole($query, $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope for team leads.
     */
    public function scopeTeamLeads($query)
    {
        return $query->where('role', 'lead');
    }
}
