<?php

namespace Webkul\Sales\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Webkul\User\Models\User;
use Webkul\Sales\Contracts\SalesTeam as SalesTeamContract;

class SalesTeam extends Model implements SalesTeamContract
{
    protected $table = 'crm_sales_teams';

    protected $fillable = [
        'name',
        'description',
        'team_lead_id',
        'status',
        'regions',
        'created_by',
    ];

    protected $casts = [
        'regions' => 'array',
    ];

    /**
     * Get the team lead.
     */
    public function teamLead(): BelongsTo
    {
        return $this->belongsTo(User::class, 'team_lead_id');
    }

    /**
     * Get the user who created the team.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the team members.
     */
    public function teamMembers(): Has<PERSON>any
    {
        return $this->hasMany(SalesTeamMember::class);
    }

    /**
     * Get active team members.
     */
    public function activeMembers(): HasMany
    {
        return $this->teamMembers()->where('status', 'active');
    }

    /**
     * Get the users in this team.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'crm_sales_team_members')
            ->withPivot(['role', 'joined_date', 'left_date', 'status'])
            ->withTimestamps();
    }

    /**
     * Get active users in this team.
     */
    public function activeUsers(): BelongsToMany
    {
        return $this->users()->wherePivot('status', 'active');
    }

    /**
     * Scope for active teams.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get team performance summary.
     */
    public function getPerformanceSummary($financialYear = null, $targetType = 'revenue')
    {
        $userIds = $this->activeUsers()->pluck('users.id');
        
        $query = SalesTarget::whereIn('user_id', $userIds)
            ->where('target_type', $targetType);
            
        if ($financialYear) {
            $query->where('financial_year', $financialYear);
        }
        
        return $query->selectRaw('
            SUM(target_value) as total_target,
            SUM(achieved_value) as total_achieved,
            AVG(achievement_percentage) as avg_achievement_percentage
        ')->first();
    }
}
