@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.targets.title') }}
@stop

@section('content')
    <div class="content">
        <div class="flex justify-between items-center mt-[10px] max-sm:flex-wrap">
            <div class="flex flex-col gap-[8px]">
                <h1>{{ __('sales::app.targets.title') }}</h1>
                <p>{{ __('sales::app.targets.description') }}</p>
            </div>
            
            <div class="flex gap-x-[10px] items-center">
                <a href="{{ route('admin.sales.targets.create') }}" class="primary-button">
                    <span class="icon-plus text-white"></span> {{ __('sales::app.targets.create') }}
                </a>
            </div>
        </div>

        <x-admin::layouts.filter>
            <x-slot:form>
                <form method="GET" action="{{ route('admin.sales.targets.index') }}">
                    <div class="flex gap-[16px] items-center">
                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                {{ __('sales::app.targets.filters.financial-year') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control
                                type="select"
                                name="financial_year"
                                :value="request('financial_year')"
                                :options="$financialYears"
                                placeholder="{{ __('sales::app.targets.filters.all-years') }}"
                            />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                {{ __('sales::app.targets.filters.period-type') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control
                                type="select"
                                name="period_type"
                                :value="request('period_type')"
                                :options="[
                                    ['value' => 'annual', 'label' => __('sales::app.targets.filters.annual')],
                                    ['value' => 'half_yearly', 'label' => __('sales::app.targets.filters.half-yearly')],
                                    ['value' => 'quarterly', 'label' => __('sales::app.targets.filters.quarterly')],
                                    ['value' => 'monthly', 'label' => __('sales::app.targets.filters.monthly')],
                                    ['value' => 'custom', 'label' => __('sales::app.targets.filters.custom')],
                                ]"
                                placeholder="{{ __('sales::app.targets.filters.all-periods') }}"
                            />
                        </x-admin::form.control-group>

                        <div id="custom_date_range" class="flex gap-[16px] items-center" style="display: none;">
                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    {{ __('sales::app.targets.filters.custom-from') }}
                                </x-admin::form.control-group.label>

                                <x-admin::form.control
                                    type="date"
                                    name="custom_from"
                                    :value="request('custom_from')"
                                />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    {{ __('sales::app.targets.filters.custom-to') }}
                                </x-admin::form.control-group.label>

                                <x-admin::form.control
                                    type="date"
                                    name="custom_to"
                                    :value="request('custom_to')"
                                />
                            </x-admin::form.control-group>
                        </div>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                {{ __('sales::app.targets.filters.user') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control
                                type="select"
                                name="user_id"
                                :value="request('user_id')"
                                :options="$users->pluck('name', 'id')"
                                placeholder="{{ __('sales::app.targets.filters.all-users') }}"
                            />
                        </x-admin::form.control-group>

                        <div class="flex gap-[16px] items-center">
                            <button type="submit" class="primary-button">
                                {{ __('sales::app.targets.filters.apply') }}
                            </button>

                            <a href="{{ route('admin.sales.targets.index') }}" class="secondary-button">
                                {{ __('sales::app.targets.filters.clear') }}
                            </a>
                        </div>
                    </div>
                </form>
            </x-slot:form>
        </x-admin::layouts.filter>

        <x-admin::table>
            <x-slot:head>
                <x-admin::table.thead>
                    <x-admin::table.tr>
                        <x-admin::table.th>{{ __('sales::app.targets.table.user') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.period') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.target-value') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.achieved-value') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.progress') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.status') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.actions') }}</x-admin::table.th>
                    </x-admin::table.tr>
                </x-admin::table.thead>
            </x-slot:head>

            <x-slot:body>
                @foreach ($targets as $target)
                    <x-admin::table.tr>
                        <x-admin::table.td>{{ $target->user->name }}</x-admin::table.td>
                        <x-admin::table.td>{{ ucfirst(str_replace('_', ' ', $target->period_type)) }} {{ $target->period_value }}, {{ $target->financial_year }}</x-admin::table.td>
                        <x-admin::table.td>{{ number_format($target->target_value, 2) }}</x-admin::table.td>
                        <x-admin::table.td>{{ number_format($target->achieved_value, 2) }}</x-admin::table.td>
                        <x-admin::table.td>
                            @php
                                $percentage = $target->target_value > 0 ? (($target->achieved_value ?? 0) / $target->target_value) * 100 : 0;
                            @endphp
                            <div class="w-full h-[20px] bg-gray-200 rounded-full dark:bg-gray-700">
                                <div class="h-[20px] rounded-full {{ $percentage >= 100 ? 'bg-green-600' : ($percentage >= 75 ? 'bg-yellow-500' : 'bg-red-600') }}" style="width: {{ min($percentage, 100) }}%;" aria-valuenow="{{ $percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </x-admin::table.td>
                        <x-admin::table.td>
                            @if($percentage >= 100)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Target Met</span>
                            @elseif($percentage >= 75)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">On Track</span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Below Target</span>
                            @endif
                        </x-admin::table.td>
                        <x-admin::table.td>
                            <div class="flex gap-x-[10px] items-center">
                                <a href="{{ route('admin.sales.targets.edit', $target->id) }}" class="transparent-button">
                                    <span class="icon-edit"></span>
                                </a>
                                <form action="{{ route('admin.sales.targets.destroy', $target->id) }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="transparent-button text-red-600">
                                        <span class="icon-delete"></span>
                                    </button>
                                </form>
                            </div>
                        </x-admin::table.td>
                    </x-admin::table.tr>
                @endforeach
            </x-slot:body>
        </x-admin::table>

        @if(method_exists($targets, 'links'))
            <div class="flex justify-end mt-[16px]">
                {{ $targets->links() }}
            </div>
        @endif
    </div>
@stop

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const periodType = document.getElementById('period_type');
        const customDateRange = document.getElementById('custom_date_range');

        function toggleCustomDateRange() {
            if (periodType.value === 'custom') {
                customDateRange.style.display = 'flex';
            } else {
                customDateRange.style.display = 'none';
            }
        }

        periodType.addEventListener('change', toggleCustomDateRange);

        // Initial check
        toggleCustomDateRange();
    });
</script>
@endpush

