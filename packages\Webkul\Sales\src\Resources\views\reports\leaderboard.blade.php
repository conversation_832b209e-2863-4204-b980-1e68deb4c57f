<x-admin::layouts>
    <x-slot:title>
        @lang('sales::app.reports.leaderboard')
    </x-slot>

    <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="flex cursor-pointer items-center">
                    <x-admin::breadcrumbs name="sales.reports.leaderboard" />
                </div>

                <div class="text-xl font-bold dark:text-white">
                    Sales Leaderboard
                </div>

                <div class="text-gray-600 dark:text-gray-300">
                    View top performing sales representatives based on achievement percentage
                </div>
            </div>

            <div class="flex items-center gap-x-2.5">
                <a href="{{ route('admin.sales.reports.index') }}" class="secondary-button">
                    <span class="icon-arrow-left text-2xl"></span>
                    Back to Reports
                </a>
            </div>
        </div>

        @if(isset($topPerformers) && count($topPerformers) > 0)
            <!-- Top 3 Performers -->
            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                @foreach($topPerformers->take(3) as $index => $performer)
                    <div class="relative rounded-lg border-2 bg-white p-6 text-center dark:bg-gray-900
                        {{ $index == 0 ? 'border-yellow-400 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20' :
                           ($index == 1 ? 'border-blue-400 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20' :
                            'border-gray-400 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800/20 dark:to-gray-700/20') }}">

                        <div class="absolute -top-3 left-4">
                            <span class="inline-flex h-6 w-6 items-center justify-center rounded-full text-xs font-bold text-white
                                {{ $index == 0 ? 'bg-yellow-500' : ($index == 1 ? 'bg-blue-500' : 'bg-gray-500') }}">
                                {{ $index + 1 }}
                            </span>
                        </div>

                        <div class="mb-4">
                            @if($index == 0)
                                <span class="icon-crown text-6xl text-yellow-500"></span>
                            @elseif($index == 1)
                                <span class="icon-medal text-6xl text-blue-500"></span>
                            @else
                                <span class="icon-award text-6xl text-gray-500"></span>
                            @endif
                        </div>

                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                            {{ $performer->user->name ?? 'N/A' }}
                        </h3>

                        <div class="mt-2 text-3xl font-bold
                            {{ $index == 0 ? 'text-yellow-600 dark:text-yellow-400' :
                               ($index == 1 ? 'text-blue-600 dark:text-blue-400' :
                                'text-gray-600 dark:text-gray-400') }}">
                            {{ number_format($performer->achievement_percentage, 1) }}%
                        </div>

                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">Achievement Rate</p>

                        <div class="mt-4 text-xs text-gray-500 dark:text-gray-400">
                            <div>Target: ${{ number_format($performer->target_value, 0) }}</div>
                            <div>Actual: ${{ number_format($performer->achieved_value, 0) }}</div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Full Leaderboard -->
            <div class="rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900">
                <div class="border-b border-gray-200 px-6 py-4 dark:border-gray-800">
                    <h3 class="flex items-center text-lg font-semibold text-gray-900 dark:text-white">
                        <span class="icon-trophy mr-2 text-xl"></span>
                        Complete Leaderboard
                    </h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
                        <thead class="bg-gray-50 dark:bg-gray-800">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Rank
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Sales Representative
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Target Amount
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Actual Amount
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Achievement %
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                                    Performance
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
                            @foreach($topPerformers as $index => $performer)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800
                                    {{ $index < 3 ? ($index == 0 ? 'bg-yellow-50 dark:bg-yellow-900/10' :
                                                    ($index == 1 ? 'bg-blue-50 dark:bg-blue-900/10' :
                                                     'bg-gray-50 dark:bg-gray-800/50')) : '' }}">
                                    <td class="whitespace-nowrap px-6 py-4">
                                        <span class="inline-flex h-8 w-8 items-center justify-center rounded-full text-xs font-bold text-white
                                            {{ $index == 0 ? 'bg-yellow-500' :
                                               ($index == 1 ? 'bg-blue-500' :
                                                ($index == 2 ? 'bg-gray-500' : 'bg-gray-400')) }}">
                                            {{ $index + 1 }}
                                        </span>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4">
                                        <div class="flex items-center">
                                            @if($index < 3)
                                                <span class="mr-2 text-lg
                                                    {{ $index == 0 ? 'icon-crown text-yellow-500' :
                                                       ($index == 1 ? 'icon-medal text-blue-500' :
                                                        'icon-award text-gray-500') }}"></span>
                                            @endif
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ $performer->user->name ?? 'N/A' }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                                        ${{ number_format($performer->target_value, 2) }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                                        ${{ number_format($performer->achieved_value, 2) }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4">
                                        <div class="flex items-center space-x-2">
                                            <div class="h-2 w-24 rounded-full bg-gray-200 dark:bg-gray-700">
                                                <div class="h-2 rounded-full
                                                    {{ $performer->achievement_percentage >= 100 ? 'bg-green-500' :
                                                       ($performer->achievement_percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500') }}"
                                                     style="width: {{ min($performer->achievement_percentage, 100) }}%">
                                                </div>
                                            </div>
                                            <span class="inline-flex rounded-full px-2 text-xs font-semibold leading-5
                                                {{ $performer->achievement_percentage >= 100 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                                   ($performer->achievement_percentage >= 75 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                                    'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200') }}">
                                                {{ number_format($performer->achievement_percentage, 1) }}%
                                            </span>
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-sm">
                                        @if($performer->achievement_percentage >= 100)
                                            <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                                                <span class="icon-success mr-1"></span>
                                                Exceeded
                                            </span>
                                        @elseif($performer->achievement_percentage >= 90)
                                            <span class="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                <span class="icon-star mr-1"></span>
                                                Excellent
                                            </span>
                                        @elseif($performer->achievement_percentage >= 75)
                                            <span class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                <span class="icon-thumbs-up mr-1"></span>
                                                Good
                                            </span>
                                        @else
                                            <span class="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-200">
                                                <span class="icon-arrow-up mr-1"></span>
                                                Needs Improvement
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @else
            <div class="rounded-lg border border-gray-200 bg-white p-8 text-center dark:border-gray-800 dark:bg-gray-900">
                <span class="icon-trophy text-6xl text-gray-300 dark:text-gray-600"></span>
                <h3 class="mt-4 text-lg font-semibold text-gray-900 dark:text-white">No Performance Data</h3>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
                    No sales performance data available to display leaderboard.
                </p>
                <div class="mt-6">
                    <a href="{{ route('admin.sales.targets.index') }}" class="primary-button">
                        Set Up Targets
                    </a>
                </div>
            </div>
        @endif
    </div>
</x-admin::layouts>
