<x-admin::layouts>
    <!-- Page Title -->
    <x-slot:title>
        @lang('admin::app.leads.bulk-upload.title')
    </x-slot>

    <div class="flex gap-4 justify-between items-center max-sm:flex-wrap">
        <p class="text-xl text-gray-800 dark:text-white font-bold">
            @lang('admin::app.leads.bulk-upload.title')
        </p>

        <div class="flex gap-x-2.5 items-center">
            <a 
                href="{{ route('admin.leads.bulk_upload.download_sample') }}" 
                class="primary-button"
            >
                @lang('admin::app.leads.bulk-upload.download-sample')
            </a>
        </div>
    </div>

    <div class="flex gap-2.5 mt-3.5 max-xl:flex-wrap">
        <!-- Left Component -->
        <div class="flex flex-col gap-2 flex-1 max-xl:flex-auto">
            <div class="p-4 bg-white dark:bg-gray-900 rounded box-shadow">
                <p class="text-base text-gray-800 dark:text-white font-semibold mb-4">
                    @lang('admin::app.leads.bulk-upload.instructions')
                </p>

                <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2">
                    <li>@lang('admin::app.leads.bulk-upload.instruction-1')</li>
                    <li>@lang('admin::app.leads.bulk-upload.instruction-2')</li>
                    <li>@lang('admin::app.leads.bulk-upload.instruction-3')</li>
                    <li>@lang('admin::app.leads.bulk-upload.instruction-4')</li>
                    <li>@lang('admin::app.leads.bulk-upload.instruction-5')</li>
                </ul>
            </div>

            <div class="p-4 bg-white dark:bg-gray-900 rounded box-shadow">
                <p class="text-base text-gray-800 dark:text-white font-semibold mb-4">
                    @lang('admin::app.leads.bulk-upload.column-description')
                </p>

                <div class="grid grid-cols-1 gap-4">
                    <div class="border-b pb-2">
                        <p class="font-medium">title <span class="text-red-500">*</span></p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.title-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">description</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.description-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">lead_value</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.lead-value-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">status</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.status-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">expected_close_date</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.expected-close-date-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">person_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.person-name-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">user_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.user-name-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">source_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.source-name-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">type_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.type-name-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">pipeline_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.pipeline-name-desc')</p>
                    </div>

                    <div class="border-b pb-2">
                        <p class="font-medium">stage_name</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">@lang('admin::app.leads.bulk-upload.stage-name-desc')</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Component -->
        <div class="flex-1 max-xl:flex-auto">
            <div class="bg-white dark:bg-gray-900 rounded box-shadow">
                <p class="p-4 text-base text-gray-800 dark:text-white font-semibold">
                    @lang('admin::app.leads.bulk-upload.upload-file')
                </p>

                <div class="p-4 border-t dark:border-gray-800">
                    <x-admin::form 
                        :action="route('admin.leads.bulk_upload.upload')"
                        method="POST"
                        enctype="multipart/form-data"
                    >
                        <div class="mb-4">
                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('admin::app.leads.bulk-upload.file')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="file"
                                    name="file"
                                    rules="required"
                                    :label="trans('admin::app.leads.bulk-upload.file')"
                                    accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                                />

                                <x-admin::form.control-group.error control-name="file" />
                            </x-admin::form.control-group>
                        </div>

                        <div class="flex justify-end">
                            <button 
                                type="submit"
                                class="primary-button"
                            >
                                @lang('admin::app.leads.bulk-upload.upload')
                            </button>
                        </div>
                    </x-admin::form>
                </div>

                @if (session()->has('import_errors'))
                    <div class="p-4 border-t dark:border-gray-800">
                        <p class="text-base text-red-600 font-semibold mb-2">
                            @lang('admin::app.leads.bulk-upload.errors')
                        </p>

                        <div class="max-h-80 overflow-y-auto">
                            <ul class="list-disc list-inside text-red-600 space-y-1">
                                @foreach (session('import_errors') as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-admin::layouts>
