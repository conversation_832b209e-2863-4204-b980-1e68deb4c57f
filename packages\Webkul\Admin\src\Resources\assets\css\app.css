@tailwind base;
@tailwind components;
@tailwind utilities;

/* -------------------------------- new css -------------------------------- */

@font-face {
    font-family: 'icomoon';
    src: url('../fonts/icomoon.woff?w2trdd') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: block;
  }

  [class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

@layer components {
    .icon-bookmark:before {
        content: "\e92c";
    }

    .icon-bookmark-active:before {
        content: "\e92d";
    }

    .icon-stats-down:before {
        content: "\e94d";
    }

    .icon-error:before {
        content: "\e951";
    }

    .icon-info:before {
        content: "\e952";
    }

    .icon-success:before {
        content: "\e953";
    }

    .icon-warning:before {
        content: "\e954";
    }

    .icon-stats-down:before {
        content: "\e94d";
    }

    .icon-stats-up:before {
        content: "\e94f";
    }

    .icon-attribute:before {
        content: "\e947";
    }

    .icon-download:before {
        content: "\e948";
    }

    .icon-settings-warehouse:before {
        content: "\e94e";
    }

    .icon-move:before {
        content: "\e949";
    }

    .icon-organization:before {
        content: "\e94a";
    }

    .icon-role:before {
        content: "\e94b";
    }

    .icon-user:before {
        content: "\e94c";
    }

    .icon-system-generate:before {
        content: "\e950";
    }

    .icon-settings-attributes:before {
        content: "\e93b";
    }

    .icon-settings-flow:before {
        content: "\e93c";
    }

    .icon-settings-group:before {
        content: "\e93d";
    }

    .icon-settings-mail:before {
        content: "\e93e";
    }

    .icon-settings-pipeline:before {
        content: "\e93f";
    }

    .icon-settings-roles:before {
        content: "\e940";
    }

    .icon-settings-sources:before {
        content: "\e941";
    }

    .icon-settings-tag:before {
        content: "\e942";
    }

    .icon-settings-type:before {
        content: "\e943";
    }

    .icon-settings-user:before {
        content: "\e944";
    }

    .icon-settings-webforms:before {
        content: "\e945";
    }

    .icon-settings-webhooks:before {
        content: "\e946";
    }

    .icon-attached-file:before {
        content: "\e932";
    }

    .icon-forward:before {
        content: "\e933";
    }

    .icon-location:before {
        content: "\e934";
    }

    .icon-pin:before {
        content: "\e935";
    }

    .icon-print:before {
        content: "\e936";
    }

    .icon-reply-all:before {
        content: "\e937";
    }

    .icon-reply:before {
        content: "\e938";
    }

    .icon-rotten:before {
        content: "\e939";
    }

    .icon-tag:before {
        content: "\e93a";
    }

    .icon-list:before {
        content: "\e92e";
    }

    .icon-enter:before {
        content: "\e92f";
    }

    .icon-kanban:before {
        content: "\e930";
    }

    .icon-tick:before {
        content: "\e931";
    }

    .icon-eye-hide:before {
        content: "\e929";
    }

    .icon-percentage:before {
        content: "\e92a";
    }

    .icon-dollar:before {
        content: "\e92b";
    }

    .icon-radio-selected:before {
        content: "\e924";
    }

    .icon-radio-normal:before {
        content: "\e925";
    }

    .icon-folder:before {
        content: "\e926";
    }

    .icon-file:before {
        content: "\e927";
    }

    .icon-eye:before {
        content: "\e928";
    }

    .icon-notification:before {
        content: "\e900";
    }

    .icon-configuration:before {
        content: "\e901";
    }

    .icon-note:before {
        content: "\e902";
    }

    .icon-edit:before {
        content: "\e903";
    }

    .icon-calendar:before {
        content: "\e904";
    }

    .icon-delete:before {
        content: "\e905";
    }

    .icon-more:before {
        content: "\e906";
    }

    .icon-checkbox-multiple:before {
        content: "\e907";
    }

    .icon-checkbox-select:before {
        content: "\e908";
    }

    .icon-checkbox-outline:before {
        content: "\e909";
    }

    .icon-message:before {
        content: "\e90a";
    }

    .icon-video:before {
        content: "\e90b";
    }

    .icon-attachment:before {
        content: "\e90c";
    }

    .icon-sent:before {
        content: "\e90d";
    }

    .icon-call:before {
        content: "\e90e";
    }

    .icon-meeting:before {
        content: "\e90f";
    }

    .icon-light:before {
        content: "\e910";
    }

    .icon-dark:before {
        content: "\e911";
    }

    .icon-mail:before {
        content: "\e912";
    }

    .icon-leads:before {
        content: "\e913";
    }

    .icon-filter:before {
        content: "\e914";
    }

    .icon-setting:before {
        content: "\e915";
    }

    .icon-product:before {
        content: "\e916";
    }

    .icon-contact:before {
        content: "\e917";
    }

    .icon-activity:before {
        content: "\e918";
    }

    .icon-perosnal:before {
        content: "\e919";
    }

    .icon-quote:before {
        content: "\e91a";
    }

    .icon-dashboard:before {
        content: "\e91b";
    }

    .icon-files-empty:before {
            font-family: 'icomoon';
            content: "\eae2"; /* Adjust this value based on your icon font */
        }

    .icon-cross-large:before {
        content: "\e91c";
    }

    .icon-left-arrow:before {
        content: "\e91d";
    }

    .icon-right-arrow:before {
        content: "\e91e";
    }

    .icon-up-arrow:before {
        content: "\e91f";
    }

    .icon-down-arrow:before {
        content: "\e920";
    }

    .icon-search:before {
        content: "\e921";
    }

    .icon-add:before {
        content: "\e922";
    }

    .icon-add-2:before {
        content: "\e923";
    }

    .label-active {
        @apply max-w-max rounded-md bg-emerald-200 px-3 py-1.5 text-xs font-medium text-emerald-900;
    }

    .label-inactive {
        @apply max-w-max rounded-md bg-red-200 px-3 py-1.5 text-xs font-medium text-red-800;
    }

    [dir="ltr"] .sidebar-rounded::after {
        @apply content-[''] w-[30px] h-[30px] top-0 right-[-30px] absolute bg-no-repeat pointer-events-none bg-[url("../images/corner-clip.svg")] transition-all;
    }

    [dir="rtl"] .sidebar-rounded::before {
        @apply content-[''] w-[30px] h-[30px] top-0 left-[-30px] absolute bg-no-repeat pointer-events-none bg-[url("../images/corner-clip.svg")] transition-all rotate-90;
    }

    .dark .sidebar-rounded::after {
        @apply bg-[url("../images/dark-corner-clip.svg")]
    }

    .dark .sidebar-rounded::before {
        @apply bg-[url("../images/dark-corner-clip.svg")]
    }

    .tox.tox-silver-sink.tox-tinymce-aux {
        z-index: 99999;
    }

    .stage::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -10px;
        width: 24px;
        height: 24px;
        z-index: 1;
        border-radius: 0 0 0 25px;
        transform: translateY(-50%) rotate(45deg);
        border-right: 4px solid #f3f4f6;
        border-top: 4px solid #f3f4f6;
    }

    .dark .stage::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -10px;
        width: 24px;
        height: 24px;
        z-index: 1;
        border-radius: 0 0 0 25px;
        transform: translateY(-50%) rotate(45deg);
        border-right: 4px solid #030712;
        border-top: 4px solid #030712;
    }

    [dir="rtl"] .stage::before {
        content: '';
        position: absolute;
        top: 50%;
        left: -10px;
        width: 24px;
        height: 24px;
        z-index: 1;
        border-radius: 0 0 0 25px;
        transform: translateY(-50%) rotate(225deg);
        border-right: 4px solid #f3f4f6;
        border-top: 4px solid #f3f4f6;
    }

     [dir="rtl"] .stage::after {
        display: none;
     }

    .shimmer {
        animation-duration: 2.2s;
        animation-fill-mode: forwards;
        animation-iteration-count: infinite;
        animation-name: skeleton;
        animation-timing-function: linear;
        background: linear-gradient(
            to right,
            #f6f6f6 8%,
            #f0f0f0 18%,
            #f6f6f6 33%
        );
        background-size: 1250px 100%;
    }

    .light-shimmer-bg {
        background: linear-gradient(
            to right,
            #fafafa 8%,
            #f5f5f5 18%,
            #fafafa 33%
        );
        background-size: 1250px 100%;
    }

    .dark .shimmer {
        background: linear-gradient(
            to right,
            #1f2937 8%,
            #1a2232 18%,
            #1f2937 33%
        );
    }

    .primary-button {
        @apply bg-brandColor border border-brandColor cursor-pointer flex focus:opacity-[0.9] font-semibold gap-x-1 hover:opacity-[0.9] items-center place-content-center px-3 py-1.5 rounded-md text-gray-50 transition-all;
    }

    .secondary-button {
        @apply flex cursor-pointer place-content-center items-center gap-x-1 whitespace-nowrap rounded-md border-2 border-brandColor bg-white px-3 py-1.5 font-semibold text-brandColor transition-all hover:bg-[#eff6ff61] focus:bg-[#eff6ff61] dark:bg-gray-800 dark:text-white dark:hover:opacity-80;
    }

    .transparent-button {
        @apply flex cursor-pointer appearance-none place-content-center items-center gap-x-1 whitespace-nowrap rounded-md border-2 border-transparent px-3 py-1.5 font-semibold text-gray-600 transition-all marker:shadow hover:bg-gray-100 focus:bg-gray-100 dark:hover:bg-gray-950;
    }

    ::-webkit-scrollbar {
        width: 12px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 6px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* Firefox */
    * {
        scrollbar-width: thin;
        scrollbar-color: #888 #f1f1f1;
    }

    ::selection {
        background-color: rgba(0, 68, 242, .2);
    }

    body {
        @apply bg-gray-100 text-sm text-gray-800;
    }

    button:disabled {
        @apply cursor-not-allowed opacity-50;
    }

    button:disabled:hover {
        @apply cursor-not-allowed opacity-50;
    }

    .draggable-ghost {
        opacity: 0.5;
        background: #e0e7ff;
    }

    html.dark [class^="icon-"], html.dark [class*=" icon-"]{
        color: #d1d5db;
    }

    p {
        @apply text-[14px] !leading-[17px];
    }

    input, textarea, select {
        @apply outline-none;
    }

    .journal-scroll::-webkit-scrollbar {
        width: 14px;
        cursor: pointer;
        display: none;
    }

    .journal-scroll::-webkit-scrollbar-track {
        background-color: #fff;
        cursor: pointer;
        border-radius: 12px;
        border: 1px solid #e9e9e9;
    }

    .journal-scroll::-webkit-scrollbar-thumb {
        cursor: pointer;
        background-color: #e9e9e9;
        border-radius: 12px;
        border: 3px solid transparent;
        background-clip: content-box;
    }

    .custom-select {
        -webkit-appearance: none;
        -moz-appearance: none;
        background: transparent;
        background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
        background-repeat: no-repeat;
        background-position-x: calc(100% - 10px);
        background-position-y: 50%;
    }

    .dark .custom-select{
        background-image: url("data:image/svg+xml;utf8,<svg fill='white' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
    }

    [dir="rtl"] .custom-select {
        background-position-x: calc(100% - (100% - 10px));
    }

    .draggable-ghost {
        @apply border border-dashed !border-gray-300;
    }

    @keyframes skeleton {
        0% {
            @apply bg-[-1250px_0];
        }

        100% {
            @apply bg-[1250px_0];
        }
    }

    .required:after {
        @apply content-['*'];
    }
}

.tox .tox-toolbar__group:last-child button {
    padding: 6px 8px;
    background: #eff6ff;
    color: #2563EB;
}

.tox .tox-toolbar__group:last-child button:hover {
    background: #dbeafe;
}

.tox .tox-toolbar__group:last-child button[aria-disabled="true"] {
    @apply cursor-not-allowed opacity-50;
}

.icon-upload:before {
    content: "\eae2";
}

.icon-import:before {
    content: "\ea8d";
}
