<?php

namespace Webkul\Sales\Repositories;

use Webkul\Core\Eloquent\Repository;
use Webkul\Sales\Contracts\SalesAchievement;

class SalesAchievementRepository extends Repository
{
    /**
     * Specify model class name.
     */
    public function model(): string
    {
        return SalesAchievement::class;
    }

    /**
     * Create a new sales achievement.
     */
    public function create(array $data): SalesAchievement
    {
        $achievement = parent::create($data);

        // Update the related sales target's achievement values
        if ($achievement->sales_target_id) {
            $salesTarget = $achievement->salesTarget;
            if ($salesTarget) {
                $salesTarget->updateAchievement();
            }
        }

        return $achievement;
    }

    /**
     * Update sales achievement.
     */
    public function update(array $data, $id): SalesAchievement
    {
        $achievement = parent::update($data, $id);

        // Update the related sales target's achievement values
        if ($achievement->sales_target_id) {
            $salesTarget = $achievement->salesTarget;
            if ($salesTarget) {
                $salesTarget->updateAchievement();
            }
        }

        return $achievement;
    }

    /**
     * Delete sales achievement.
     */
    public function delete($id): bool
    {
        $achievement = $this->find($id);
        $salesTargetId = $achievement->sales_target_id;

        $result = parent::delete($id);

        // Update the related sales target's achievement values
        if ($result && $salesTargetId) {
            $salesTarget = app(\Webkul\Sales\Repositories\SalesTargetRepository::class)->find($salesTargetId);
            if ($salesTarget) {
                $salesTarget->updateAchievement();
            }
        }

        return $result;
    }

    /**
     * Get achievements for a specific user.
     */
    public function getUserAchievements(int $userId, ?string $startDate = null, ?string $endDate = null)
    {
        $query = $this->model->where('user_id', $userId);

        if ($startDate) {
            $query->where('achievement_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('achievement_date', '<=', $endDate);
        }

        return $query->with(['salesTarget', 'user'])->orderBy('achievement_date', 'desc')->get();
    }

    /**
     * Get achievements by type.
     */
    public function getAchievementsByType(string $type, ?string $startDate = null, ?string $endDate = null)
    {
        $query = $this->model->where('achievement_type', $type);

        if ($startDate) {
            $query->where('achievement_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('achievement_date', '<=', $endDate);
        }

        return $query->with(['salesTarget', 'user'])->orderBy('achievement_date', 'desc')->get();
    }

    /**
     * Get total achievements for a date range.
     */
    public function getTotalAchievements(?string $startDate = null, ?string $endDate = null): float
    {
        $query = $this->model->query();

        if ($startDate) {
            $query->where('achievement_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('achievement_date', '<=', $endDate);
        }

        return $query->sum('achievement_value') ?? 0;
    }

    /**
     * Get achievements summary by user.
     */
    public function getAchievementsSummaryByUser(?string $startDate = null, ?string $endDate = null)
    {
        $query = $this->model->query();

        if ($startDate) {
            $query->where('achievement_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('achievement_date', '<=', $endDate);
        }

        return $query
            ->selectRaw('user_id, SUM(achievement_value) as total_achievement, COUNT(*) as achievement_count')
            ->groupBy('user_id')
            ->with('user')
            ->orderBy('total_achievement', 'desc')
            ->get();
    }
}
