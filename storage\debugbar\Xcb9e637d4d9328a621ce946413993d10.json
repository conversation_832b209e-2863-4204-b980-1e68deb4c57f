{"__meta": {"id": "Xcb9e637d4d9328a621ce946413993d10", "datetime": "2025-04-15 23:58:45", "utime": **********.709223, "method": "PUT", "uri": "/admin/leads/stage/edit/5", "ip": "127.0.0.1"}, "php": {"version": "8.2.25", "interface": "cli-server"}, "messages": {"count": 7, "messages": [{"message": "[23:58:45] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$app is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.332528, "xdebug_link": null, "collector": "log"}, {"message": "[23:58:45] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$request is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 22", "message_html": null, "is_string": false, "label": "warning", "time": **********.332683, "xdebug_link": null, "collector": "log"}, {"message": "[23:58:45] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php on line 148", "message_html": null, "is_string": false, "label": "warning", "time": **********.481177, "xdebug_link": null, "collector": "log"}, {"message": "[23:58:45] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php on line 148", "message_html": null, "is_string": false, "label": "warning", "time": **********.481422, "xdebug_link": null, "collector": "log"}, {"message": "[23:58:45] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php on line 148", "message_html": null, "is_string": false, "label": "warning", "time": **********.481646, "xdebug_link": null, "collector": "log"}, {"message": "[23:58:45] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php on line 148", "message_html": null, "is_string": false, "label": "warning", "time": **********.65315, "xdebug_link": null, "collector": "log"}, {"message": "[23:58:45] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php on line 148", "message_html": null, "is_string": false, "label": "warning", "time": **********.675913, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1744741724.96201, "end": **********.70925, "duration": 0.7472400665283203, "duration_str": "747ms", "measures": [{"label": "Booting", "start": 1744741724.96201, "relative_start": 0, "end": **********.295337, "relative_end": **********.295337, "duration": 0.333327054977417, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.295349, "relative_start": 0.33333897590637207, "end": **********.709252, "relative_end": 2.1457672119140625e-06, "duration": 0.41390323638916016, "duration_str": "414ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 28951232, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT admin/leads/stage/edit/{id}", "middleware": "web, admin_locale, user", "controller": "Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@updateStage", "namespace": null, "prefix": "admin/leads", "where": [], "as": "admin.leads.stage.update", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FLead%2FLeadController.php&line=333\" onclick=\"\">packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php:333-375</a>"}, "queries": {"nb_statements": 30, "nb_visible_statements": 31, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08525999999999999, "accumulated_duration_str": "85.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 557}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}], "start": **********.341443, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:1152", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=1152", "ajax": false, "filename": "BaseRepository.php", "line": "1152"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `core_config` where `code` = 'general.general.locale_settings.locale'", "type": "query", "params": [], "bindings": ["general.general.locale_settings.locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Core.php", "line": 241}], "start": **********.345041, "duration": 0.01575, "duration_str": "15.75ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 18.473}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.370581, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 18.473, "width_percent": 0.504}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, {"index": 22, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "admin_locale", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3742151, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "user:56", "source": {"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=56", "ajax": false, "filename": "Bouncer.php", "line": "56"}, "connection": "laravel-crm", "explain": null, "start_percent": 18.977, "width_percent": 0.516}, {"sql": "select * from `leads` where `leads`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 71}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 339}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.388467, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Repository.php:71", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=71", "ajax": false, "filename": "Repository.php", "line": "71"}, "connection": "laravel-crm", "explain": null, "start_percent": 19.493, "width_percent": 0.528}, {"sql": "select * from `lead_pipeline_stages` where `lead_pipeline_stages`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 342}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.390977, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 20.021, "width_percent": 0.469}, {"sql": "select * from `lead_pipelines` where `lead_pipelines`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 348}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.39304, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 20.49, "width_percent": 0.399}, {"sql": "select * from `lead_pipeline_stages` where `lead_pipeline_stages`.`lead_pipeline_id` = 1 and `lead_pipeline_stages`.`lead_pipeline_id` is not null and `id` = 5 order by `sort_order` asc limit 1", "type": "query", "params": [], "bindings": [1, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 350}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.395829, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:350", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 350}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FLead%2FLeadController.php&line=350", "ajax": false, "filename": "LeadController.php", "line": "350"}, "connection": "laravel-crm", "explain": null, "start_percent": 20.889, "width_percent": 0.446}, {"sql": "select * from `lead_pipeline_stages` where `lead_pipeline_stages`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 54}, {"index": 20, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 173}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 364}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.397936, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Repository.php:54", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=54", "ajax": false, "filename": "Repository.php", "line": "54"}, "connection": "laravel-crm", "explain": null, "start_percent": 21.335, "width_percent": 0.387}, {"sql": "select * from `leads` where `leads`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 364}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.400152, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "laravel-crm", "explain": null, "start_percent": 21.722, "width_percent": 0.422}, {"sql": "update `leads` set `lead_value` = '10000', `closed_at` = '2025-04-15 23:58:45', `lead_pipeline_stage_id` = 5, `leads`.`updated_at` = '2025-04-15 23:58:45' where `id` = 5", "type": "query", "params": [], "bindings": ["10000", "2025-04-15 23:58:45", 5, "2025-04-15 23:58:45", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 364}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.441582, "duration": 0.027399999999999997, "duration_str": "27.4ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "laravel-crm", "explain": null, "start_percent": 22.144, "width_percent": 32.137}, {"sql": "select * from `attributes` where `code` = 'entity'", "type": "query", "params": [], "bindings": ["entity"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 23}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 109}, {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 51}], "start": **********.473295, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "laravel-crm", "explain": null, "start_percent": 54.281, "width_percent": 0.962}, {"sql": "select * from `attributes` where `entity_type` = 'leads'", "type": "query", "params": [], "bindings": ["leads"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 99}, {"index": 16, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 62}, {"index": 17, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 25, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 182}], "start": **********.477216, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:99", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=99", "ajax": false, "filename": "CustomAttribute.php", "line": "99"}, "connection": "laravel-crm", "explain": null, "start_percent": 55.243, "width_percent": 0.891}, {"sql": "select * from `attributes` where `code` = 'attribute'", "type": "query", "params": [], "bindings": ["attribute"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 23}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 109}, {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 51}], "start": **********.482064, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "laravel-crm", "explain": null, "start_percent": 56.134, "width_percent": 0.786}, {"sql": "insert into `activities` (`type`, `title`, `is_done`, `additional`, `user_id`, `updated_at`, `created_at`) values ('system', 'Updated closed_at', 1, '{\\\"attribute\\\":\\\"closed_at\\\",\\\"new\\\":{\\\"value\\\":\\\"2025-04-15 23:58:45\\\",\\\"label\\\":\\\"2025-04-15 23:58:45\\\"},\\\"old\\\":{\\\"value\\\":null,\\\"label\\\":null}}', 1, '2025-04-15 23:58:45', '2025-04-15 23:58:45')", "type": "query", "params": [], "bindings": ["system", "Updated closed_at", 1, "{\"attribute\":\"closed_at\",\"new\":{\"value\":\"2025-04-15 23:58:45\",\"label\":\"2025-04-15 23:58:45\"},\"old\":{\"value\":null,\"label\":null}}", 1, "2025-04-15 23:58:45", "2025-04-15 23:58:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Activity/src/Repositories/ActivityRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Repositories\\ActivityRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 74}, {"index": 18, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 42}, {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}], "start": **********.510105, "duration": 0.00833, "duration_str": "8.33ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 56.92, "width_percent": 9.77}, {"sql": "insert into `lead_activities` (`activity_id`, `lead_id`) values (1, 5)", "type": "query", "params": [], "bindings": [1, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 95}, {"index": 12, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 20, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 182}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 364}], "start": **********.6003392, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:95", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=95", "ajax": false, "filename": "LogsActivity.php", "line": "95"}, "connection": "laravel-crm", "explain": null, "start_percent": 66.69, "width_percent": 2.991}, {"sql": "select * from `attributes` where (`entity_type` = 'leads') and `code` in ('lead_pipeline_stage_id', 'lead_value')", "type": "query", "params": [], "bindings": ["leads", "lead_pipeline_stage_id", "lead_value"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 203}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 364}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.621701, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "LeadRepository.php:203", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 203}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FLead%2Fsrc%2FRepositories%2FLeadRepository.php&line=203", "ajax": false, "filename": "LeadRepository.php", "line": "203"}, "connection": "laravel-crm", "explain": null, "start_percent": 69.681, "width_percent": 0.833}, {"sql": "select * from `attribute_values` where `entity_type` = 'leads' and `entity_id` = 5 and `attribute_id` = 111", "type": "query", "params": [], "bindings": ["leads", 5, 111], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 87}, {"index": 19, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}], "start": **********.624482, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 70.514, "width_percent": 1.454}, {"sql": "insert into `attribute_values` (`entity_type`, `entity_id`, `attribute_id`, `integer_value`) values ('leads', 5, 111, 5)", "type": "query", "params": [], "bindings": ["leads", 5, 111, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 17, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 364}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.62807, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 71.968, "width_percent": 2.58}, {"sql": "select * from `leads` where `leads`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 30, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 31, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}, {"index": 32, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 364}], "start": **********.6510499, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:17", "source": {"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=17", "ajax": false, "filename": "LogsActivity.php", "line": "17"}, "connection": "laravel-crm", "explain": null, "start_percent": 74.548, "width_percent": 0.68}, {"sql": "select * from `attributes` where `attributes`.`id` = 111 limit 1", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 72}, {"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 29, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 30, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 31, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}], "start": **********.653424, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:72", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=72", "ajax": false, "filename": "LogsActivity.php", "line": "72"}, "connection": "laravel-crm", "explain": null, "start_percent": 75.229, "width_percent": 0.54}, {"sql": "select `id` as `id`, `name` as `name` from `lead_pipeline_stages` where `lead_pipeline_stages`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 54}, {"index": 20, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 172}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 176}, {"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 105}, {"index": 23, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 82}], "start": **********.655536, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Repository.php:54", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=54", "ajax": false, "filename": "Repository.php", "line": "54"}, "connection": "laravel-crm", "explain": null, "start_percent": 75.768, "width_percent": 0.504}, {"sql": "insert into `activities` (`type`, `title`, `is_done`, `additional`, `user_id`, `updated_at`, `created_at`) values ('system', 'Updated Stage', 1, '{\\\"attribute\\\":\\\"Stage\\\",\\\"new\\\":{\\\"value\\\":5,\\\"label\\\":\\\"Won\\\"},\\\"old\\\":{\\\"value\\\":null,\\\"label\\\":null}}', 1, '2025-04-15 23:58:45', '2025-04-15 23:58:45')", "type": "query", "params": [], "bindings": ["system", "Updated Stage", 1, "{\"attribute\":\"Stage\",\"new\":{\"value\":5,\"label\":\"Won\"},\"old\":{\"value\":null,\"label\":null}}", 1, "2025-04-15 23:58:45", "2025-04-15 23:58:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Activity/src/Repositories/ActivityRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Repositories\\ActivityRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 74}, {"index": 18, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}], "start": **********.657975, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 76.273, "width_percent": 2.311}, {"sql": "insert into `lead_activities` (`activity_id`, `lead_id`) values (2, 5)", "type": "query", "params": [], "bindings": [2, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 93}, {"index": 12, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 20, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 21, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}], "start": **********.661886, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:93", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=93", "ajax": false, "filename": "LogsActivity.php", "line": "93"}, "connection": "laravel-crm", "explain": null, "start_percent": 78.583, "width_percent": 2.135}, {"sql": "select * from `attribute_values` where `entity_type` = 'leads' and `entity_id` = 5 and `attribute_id` = 105", "type": "query", "params": [], "bindings": ["leads", 5, 105], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 87}, {"index": 19, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}], "start": **********.666367, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 80.718, "width_percent": 0.938}, {"sql": "insert into `attribute_values` (`entity_type`, `entity_id`, `attribute_id`, `float_value`) values ('leads', 5, 105, '10000')", "type": "query", "params": [], "bindings": ["leads", 5, 105, "10000"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 17, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 364}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.669671, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 81.656, "width_percent": 2.299}, {"sql": "select * from `leads` where `leads`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 30, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 31, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}, {"index": 32, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 364}], "start": **********.673921, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:17", "source": {"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=17", "ajax": false, "filename": "LogsActivity.php", "line": "17"}, "connection": "laravel-crm", "explain": null, "start_percent": 83.955, "width_percent": 0.739}, {"sql": "select * from `attributes` where `attributes`.`id` = 105 limit 1", "type": "query", "params": [], "bindings": [105], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 72}, {"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 29, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 30, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 31, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}], "start": **********.676184, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:72", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=72", "ajax": false, "filename": "LogsActivity.php", "line": "72"}, "connection": "laravel-crm", "explain": null, "start_percent": 84.694, "width_percent": 0.586}, {"sql": "insert into `activities` (`type`, `title`, `is_done`, `additional`, `user_id`, `updated_at`, `created_at`) values ('system', 'Updated Lead Value', 1, '{\\\"attribute\\\":\\\"Lead Value\\\",\\\"new\\\":{\\\"value\\\":10000,\\\"label\\\":\\\"$10,000.00\\\"},\\\"old\\\":{\\\"value\\\":null,\\\"label\\\":\\\"$0.00\\\"}}', 1, '2025-04-15 23:58:45', '2025-04-15 23:58:45')", "type": "query", "params": [], "bindings": ["system", "Updated Lead Value", 1, "{\"attribute\":\"Lead Value\",\"new\":{\"value\":10000,\"label\":\"$10,000.00\"},\"old\":{\"value\":null,\"label\":\"$0.00\"}}", 1, "2025-04-15 23:58:45", "2025-04-15 23:58:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Activity/src/Repositories/ActivityRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Repositories\\ActivityRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 74}, {"index": 18, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}], "start": **********.678757, "duration": 0.00789, "duration_str": "7.89ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 85.28, "width_percent": 9.254}, {"sql": "insert into `lead_activities` (`activity_id`, `lead_id`) values (3, 5)", "type": "query", "params": [], "bindings": [3, 5], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 93}, {"index": 12, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 20, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 21, "namespace": null, "name": "packages/Webkul/Lead/src/Repositories/LeadRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Lead\\src\\Repositories\\LeadRepository.php", "line": 206}], "start": **********.688953, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:93", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=93", "ajax": false, "filename": "LogsActivity.php", "line": "93"}, "connection": "laravel-crm", "explain": null, "start_percent": 94.534, "width_percent": 3.073}, {"sql": "select * from `workflows` where `event` = 'lead.update.after'", "type": "query", "params": [], "bindings": ["lead.update.after"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Automation/src/Listeners/Entity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Automation\\src\\Listeners\\Entity.php", "line": 27}, {"index": 18, "namespace": null, "name": "packages/Webkul/Automation/src/Providers/WorkflowServiceProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Automation\\src\\Providers\\WorkflowServiceProvider.php", "line": 24}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/LeadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\LeadController.php", "line": 370}], "start": **********.697694, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "laravel-crm", "explain": null, "start_percent": 97.607, "width_percent": 2.393}]}, "models": {"data": {"Webkul\\Attribute\\Models\\Attribute": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Lead\\Models\\Lead": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FLead%2Fsrc%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "Webkul\\Lead\\Models\\Stage": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FLead%2Fsrc%2FModels%2FStage.php&line=1", "ajax": false, "filename": "Stage.php", "line": "?"}}, "Webkul\\User\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Lead\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FLead%2Fsrc%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 24, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/leads/view/5\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/admin/leads/stage/edit/5", "status_code": "<pre class=sf-dump id=sf-dump-1620108837 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1620108837\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-746384364 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-746384364\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1694592511 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lead_pipeline_stage_id</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>lead_value</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10000</span>\"\n  \"<span class=sf-dump-key>closed_at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-04-15</span>\"\n  \"<span class=sf-dump-key>entity_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694592511\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-197702005 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">74</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImpsVGk5RHRCMGVsYUJzSS9TV1FIMEE9PSIsInZhbHVlIjoiNVVBMVNZVXVMUDlOMjM1MktNakFldDZMdWZmeUZkZFlRWFVPWjA2MjA2d0xnSWs1RFNWVTRjY3JqeEJMMXRyRXFoZENhZ3JFSzZ6T2FmMjVHaEw2MDh6MjBITDRxdWNaSjJTcnhlM3lSQStaNlY4bnFXK3FkUllzMmp1a0YyeEQiLCJtYWMiOiIzYzcyODY1OGJlNDE0Yzk3OTkzZWNjMzBhZGRjMDkyZmIxMTEyZWUwNzM4MGZmOWYwZTE4Y2I2YzE3ZDc1NzhhIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Brave&quot;;v=&quot;135&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/leads/view/5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImpsVGk5RHRCMGVsYUJzSS9TV1FIMEE9PSIsInZhbHVlIjoiNVVBMVNZVXVMUDlOMjM1MktNakFldDZMdWZmeUZkZFlRWFVPWjA2MjA2d0xnSWs1RFNWVTRjY3JqeEJMMXRyRXFoZENhZ3JFSzZ6T2FmMjVHaEw2MDh6MjBITDRxdWNaSjJTcnhlM3lSQStaNlY4bnFXK3FkUllzMmp1a0YyeEQiLCJtYWMiOiIzYzcyODY1OGJlNDE0Yzk3OTkzZWNjMzBhZGRjMDkyZmIxMTEyZWUwNzM4MGZmOWYwZTE4Y2I2YzE3ZDc1NzhhIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6InltN2l3ak8xL3VaSlpjUHR6MDM1dVE9PSIsInZhbHVlIjoiZVczbXZad3NmYU5iZ2gzUU4xMG1kQ3M5TC9LZGFhazRiODB0Nzl2dHA3Zk5aRWNqYmR5Qlp0YmZYdCs4L01IcjV5QWVjZ21nU2RVNlpWZ01KbHE0SXEvSjFNRXlNTzdDeW9BRVZidVExUFhQL3Nwb3JmeVJtYW9POHZqTzhmMGkiLCJtYWMiOiI0ZjQwZDIxMjAyNjJlMGU2OTdjMjgxMzI1MTg1OWYxY2QyZjFhMGUyOGJlNDJkNDliMjU3ZjljMTJmMDljYWY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-197702005\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-614427449 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lh4xOQcW5AWaSgG8tSFW592msyWyo1WTTiE9zQSW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614427449\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Apr 2025 18:28:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikt0bnBtVFJOdUFLVnpoZTR2ODdpSkE9PSIsInZhbHVlIjoiaU5EWVRyOXkzc3JMMEhCc3loRE95UDFSdWR0aEZFM1pHVE8wYVNESFA2YVJQWkRGU3huZTZqUEVjZkZRSTNGdUpVaXRnQVdoc3hiLzZ3NGNLRVFyVHhEQi9ld1RQMnpicE1iUUpPUmJHZkF2dllOYll6WUJlTTkvWTFQdjRKVjciLCJtYWMiOiJlN2FiYzdmNjk0OTI2NjA2YmViYjI2NGRkZGEwNzQwMjc2MDIwYWM1YmQ1MzAxYzY0MTZlMDQwMDM5MWQ1YjFiIiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 20:28:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">krayin_crm_session=eyJpdiI6ImtMUzhFaEJNWDVXVU5COC9aNFBxRXc9PSIsInZhbHVlIjoiMWlQOSszVEM1RjVZT3RySVU5a25LM2ZBVnczYWNyTDVVazgrVlFVbVIweVIrMGNSbUcrSCtFOGJYYksvMHJEYXNETTdHbldDNG9JWVdMTHRjSTVMTnZlUTFSYVRCZy9wd0xCQ0UrRnI4d21pQUJJaFBSUkJ4QWp1cldVZ1VwLzgiLCJtYWMiOiJhMTkzNGJlNmQ1MmI4ZGM0MGZkNDBjNTNmMzY5OTljNWUzM2Y3YjE1NTQ3YWY5Mjk4MDA5YzEwNTk4YzJkZjg0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 20:28:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikt0bnBtVFJOdUFLVnpoZTR2ODdpSkE9PSIsInZhbHVlIjoiaU5EWVRyOXkzc3JMMEhCc3loRE95UDFSdWR0aEZFM1pHVE8wYVNESFA2YVJQWkRGU3huZTZqUEVjZkZRSTNGdUpVaXRnQVdoc3hiLzZ3NGNLRVFyVHhEQi9ld1RQMnpicE1iUUpPUmJHZkF2dllOYll6WUJlTTkvWTFQdjRKVjciLCJtYWMiOiJlN2FiYzdmNjk0OTI2NjA2YmViYjI2NGRkZGEwNzQwMjc2MDIwYWM1YmQ1MzAxYzY0MTZlMDQwMDM5MWQ1YjFiIiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 20:28:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">krayin_crm_session=eyJpdiI6ImtMUzhFaEJNWDVXVU5COC9aNFBxRXc9PSIsInZhbHVlIjoiMWlQOSszVEM1RjVZT3RySVU5a25LM2ZBVnczYWNyTDVVazgrVlFVbVIweVIrMGNSbUcrSCtFOGJYYksvMHJEYXNETTdHbldDNG9JWVdMTHRjSTVMTnZlUTFSYVRCZy9wd0xCQ0UrRnI4d21pQUJJaFBSUkJ4QWp1cldVZ1VwLzgiLCJtYWMiOiJhMTkzNGJlNmQ1MmI4ZGM0MGZkNDBjNTNmMzY5OTljNWUzM2Y3YjE1NTQ3YWY5Mjk4MDA5YzEwNTk4YzJkZjg0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 20:28:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/leads/view/5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}