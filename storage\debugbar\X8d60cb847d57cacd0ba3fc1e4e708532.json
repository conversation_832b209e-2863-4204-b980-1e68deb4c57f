{"__meta": {"id": "X8d60cb847d57cacd0ba3fc1e4e708532", "datetime": "2025-04-15 23:04:57", "utime": 1744738497.41984, "method": "GET", "uri": "/cache/logo.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.25", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[23:04:57] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Installer\\src\\Http\\Controllers\\ImageCacheController.php on line 104", "message_html": null, "is_string": false, "label": "warning", "time": 1744738497.416931, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1744738497.092339, "end": 1744738497.419857, "duration": 0.3275179862976074, "duration_str": "328ms", "measures": [{"label": "Booting", "start": 1744738497.092339, "relative_start": 0, "end": 1744738497.37802, "relative_end": 1744738497.37802, "duration": 0.28568100929260254, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1744738497.378029, "relative_start": 0.2856900691986084, "end": 1744738497.419858, "relative_end": 9.5367431640625e-07, "duration": 0.04182887077331543, "duration_str": "41.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24105272, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cache/{filename}", "as": "image_cache", "controller": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" onclick=\"\">packages/Webkul/Installer/src/Http/Controllers/ImageCacheController.php:30-41</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3cEqcKIZL1QjQP7BFSGZS6cjnEd3ixPySuKAnanI", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/cache/logo.png", "status_code": "<pre class=sf-dump id=sf-dump-1605278762 data-indent-pad=\"  \"><span class=sf-dump-num>304</span>\n</pre><script>Sfdump(\"sf-dump-1605278762\", {\"maxDepth\":0})</script>\n", "status_text": "Not Modified", "format": "html", "content_type": "text/html", "request_query": "<pre class=sf-dump id=sf-dump-1940303465 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1940303465\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1183938593 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1183938593\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-610266867 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Brave&quot;;v=&quot;135&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkdEWHB1aCtaWVgxM25tZWJCdCs5U3c9PSIsInZhbHVlIjoiT2I2ckkzWHRZejVpYm5uSzFJNVh0eEJlUjlQZis3dmFsMUxEYlE2azJrTERvdlRRZmJJTFdiN01JMUxUVjZ3U0grMUZ0QzY2S1VBTlhJS0NkQkR0NmRadnJtdDZnR1NWeDNxV2U0T2duKzRtUXREdHZUajMyNGVzMDlBM0k2R1ciLCJtYWMiOiJmZDJmZjM5ZGQ4NTNhNDI4MjIzZjQ1MjVkMDM1NjJiNDZiYjI0OGIyZmE5YWNmOGEyNDFmZjc0MjEwZjk5NTdkIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6IkhVdkxkZzRMd3E1T2xDNnlOUVZ6aGc9PSIsInZhbHVlIjoicGwvMEd4YVNFZjI5TStkY1MyRU5QMmlSNHI4akIyRkhEY0pNcDNNY1dXclNleFJDaUFYYWMwVTM0TVFMYWZWdEtBWEdsZGwrSGh0MENtd3ZscUdUeVlYRzFIengvc2RueVBmNGl1V1JjVVMxZWE1R2xneHJNS0prczBKR0NDcFYiLCJtYWMiOiIyM2IwYzY1ZDcyZDliZTY0N2QyNTg1YTcwYzc3YzE3ZjQxYjJjODkzN2FkYTEzZDAzYjM2ZDkwZDZlNzU0ZDgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-none-match</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610266867\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1269553537 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdEWHB1aCtaWVgxM25tZWJCdCs5U3c9PSIsInZhbHVlIjoiT2I2ckkzWHRZejVpYm5uSzFJNVh0eEJlUjlQZis3dmFsMUxEYlE2azJrTERvdlRRZmJJTFdiN01JMUxUVjZ3U0grMUZ0QzY2S1VBTlhJS0NkQkR0NmRadnJtdDZnR1NWeDNxV2U0T2duKzRtUXREdHZUajMyNGVzMDlBM0k2R1ciLCJtYWMiOiJmZDJmZjM5ZGQ4NTNhNDI4MjIzZjQ1MjVkMDM1NjJiNDZiYjI0OGIyZmE5YWNmOGEyNDFmZjc0MjEwZjk5NTdkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkhVdkxkZzRMd3E1T2xDNnlOUVZ6aGc9PSIsInZhbHVlIjoicGwvMEd4YVNFZjI5TStkY1MyRU5QMmlSNHI4akIyRkhEY0pNcDNNY1dXclNleFJDaUFYYWMwVTM0TVFMYWZWdEtBWEdsZGwrSGh0MENtd3ZscUdUeVlYRzFIengvc2RueVBmNGl1V1JjVVMxZWE1R2xneHJNS0prczBKR0NDcFYiLCJtYWMiOiIyM2IwYzY1ZDcyZDliZTY0N2QyNTg1YTcwYzc3YzE3ZjQxYjJjODkzN2FkYTEzZDAzYjM2ZDkwZDZlNzU0ZDgyIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269553537\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1008280206 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=10080, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Apr 2025 17:34:57 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008280206\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1719830516 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3cEqcKIZL1QjQP7BFSGZS6cjnEd3ixPySuKAnanI</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719830516\", {\"maxDepth\":0})</script>\n"}}