<?php echo view_render_event('admin.dashboard.index.open_leads_by_states.before'); ?>


<!-- Total Leads Vue Component -->
<v-dashboard-open-leads-by-states>
    <!-- Shimmer -->
    <?php if (isset($component)) { $__componentOriginal7ef7cb73cb8ccadf1796bffcbeb1f114 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7ef7cb73cb8ccadf1796bffcbeb1f114 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.open-leads-by-states','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.open-leads-by-states'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7ef7cb73cb8ccadf1796bffcbeb1f114)): ?>
<?php $attributes = $__attributesOriginal7ef7cb73cb8ccadf1796bffcbeb1f114; ?>
<?php unset($__attributesOriginal7ef7cb73cb8ccadf1796bffcbeb1f114); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7ef7cb73cb8ccadf1796bffcbeb1f114)): ?>
<?php $component = $__componentOriginal7ef7cb73cb8ccadf1796bffcbeb1f114; ?>
<?php unset($__componentOriginal7ef7cb73cb8ccadf1796bffcbeb1f114); ?>
<?php endif; ?>
</v-dashboard-open-leads-by-states>

<?php echo view_render_event('admin.dashboard.index.open_leads_by_states.after'); ?>


<?php if (! $__env->hasRenderedOnce('582cd82d-e660-4fb0-bdfc-cfb898a7b300')): $__env->markAsRenderedOnce('582cd82d-e660-4fb0-bdfc-cfb898a7b300');
$__env->startPush('scripts'); ?>
    <script
        type="text/x-template"
        id="v-dashboard-open-leads--by-states-template"
    >
        <!-- Shimmer -->
        <template v-if="isLoading">
            <?php if (isset($component)) { $__componentOriginal7ef7cb73cb8ccadf1796bffcbeb1f114 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7ef7cb73cb8ccadf1796bffcbeb1f114 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.open-leads-by-states','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.open-leads-by-states'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7ef7cb73cb8ccadf1796bffcbeb1f114)): ?>
<?php $attributes = $__attributesOriginal7ef7cb73cb8ccadf1796bffcbeb1f114; ?>
<?php unset($__attributesOriginal7ef7cb73cb8ccadf1796bffcbeb1f114); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7ef7cb73cb8ccadf1796bffcbeb1f114)): ?>
<?php $component = $__componentOriginal7ef7cb73cb8ccadf1796bffcbeb1f114; ?>
<?php unset($__componentOriginal7ef7cb73cb8ccadf1796bffcbeb1f114); ?>
<?php endif; ?>
        </template>

        <!-- Total Sales Section -->
        <template v-else>
            <div class="grid gap-4 rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                <div class="flex flex-col justify-between gap-1">
                    <p class="text-base font-semibold dark:text-gray-300">
                        <?php echo app('translator')->get('admin::app.dashboard.index.open-leads-by-states.title'); ?>
                    </p>
                </div>

                <!-- Doughnut Chart -->
                <div
                    class="relative flex w-full max-w-full flex-col gap-4"
                    v-if="report.statistics.length"
                >
                    <canvas
                        :id="$.uid + '_chart'"
                        class="w-full max-w-full items-end px-12"
                        :style="{ height: report.statistics.length * 60 + 'px' }"
                    ></canvas>

                    <ul class="absolute flex w-full flex-col">
                        <li
                            class="flex w-full flex-col border-b border-gray-200 pb-[9px] pt-2.5 last:border-none dark:border-gray-800"
                            v-for="(stat, index) in report.statistics"
                        >
                            <span class="text-sm font-semibold dark:text-gray-300">
                                {{ stat.total }}
                            </span>

                            <span class="text-sm font-semibold dark:text-gray-300">
                                {{ stat.name }}
                            </span>
                        </li>
                    </ul>
                </div>

                <!-- Empty Product Design -->
                <div
                    class="flex flex-col gap-8 p-4"
                    v-else
                >
                    <div class="grid justify-center justify-items-center gap-3.5 py-2.5">
                        <!-- Placeholder Image -->
                        <img
                            src="<?php echo e(vite()->asset('images/empty-placeholders/default.svg')); ?>"
                            class="dark:mix-blend-exclusion dark:invert"
                        >

                        <!-- Add Variants Information -->
                        <div class="flex flex-col items-center">
                            <p class="text-base font-semibold text-gray-400">
                                <?php echo app('translator')->get('admin::app.dashboard.index.open-leads-by-states.empty-title'); ?>
                            </p>

                            <p class="text-gray-400">
                                <?php echo app('translator')->get('admin::app.dashboard.index.open-leads-by-states.empty-info'); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </script>


    <script type="module">
        app.component('v-dashboard-open-leads-by-states', {
            template: '#v-dashboard-open-leads--by-states-template',

            data() {
                return {
                    report: [],

                    isLoading: true,

                    chart: undefined,
                }
            },

            mounted() {
                this.getStats({});

                this.$emitter.on('reporting-filter-updated', this.getStats);
            },

            methods: {
                getStats(filtets) {
                    this.isLoading = true;

                    var filtets = Object.assign({}, filtets);

                    filtets.type = 'open-leads-by-states';

                    this.$axios.get("<?php echo e(route('admin.dashboard.stats')); ?>", {
                            params: filtets
                        })
                        .then(response => {
                            this.report = response.data;

                            this.isLoading = false;

                            setTimeout(() => {
                                this.prepare();
                            }, 0);
                        })
                        .catch(error => {});
                },

                prepare() {
                    if (this.chart) {
                        this.chart.destroy();
                    }

                    if (this.report.statistics.length === 0) {
                        return;
                    }

                    const ctx = document.getElementById(this.$.uid + '_chart')?.getContext('2d');

                    // Create gradient
                    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
                    gradient.addColorStop(0, 'rgba(144, 247, 236, 1)');
                    gradient.addColorStop(1, 'rgba(50, 204, 188, 1)');

                    this.chart = new Chart(ctx, {
                        type: 'funnel',

                        data: {
                            labels: this.report.statistics.map(stat => stat.name),
                            datasets: [
                                {
                                    data: this.report.statistics.map(stat => stat.total),
                                    backgroundColor: gradient,
                                    borderColor: 'rgba(0, 0, 0, 0)',
                                    borderWidth: 0,
                                },
                            ],
                        },

                        options: {
                            indexAxis: 'y',
                        },
                    });
                }
            }
        });
    </script>
<?php $__env->stopPush(); endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/dashboard/index/open-leads-by-states.blade.php ENDPATH**/ ?>