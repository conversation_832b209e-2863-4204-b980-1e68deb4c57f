<?php

namespace Webkul\Sales\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Webkul\User\Models\User;
use Webkul\Sales\Contracts\SalesTarget as SalesTargetContract;

class SalesTarget extends Model implements SalesTargetContract
{
    protected $table = 'sales_targets';

    protected $fillable = [
        'user_id',
        'financial_year',
        'period_type',
        'quarter',
        'month',
        'target_type',
        'target_value',
        'achieved_value',
        'achievement_percentage',
        'start_date',
        'end_date',
        'status',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'target_value' => 'decimal:2',
        'achieved_value' => 'decimal:2',
        'achievement_percentage' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    /**
     * Get the user that owns the target.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who created the target.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the target.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the achievements for this target.
     */
    public function achievements(): HasMany
    {
        return $this->hasMany(SalesAchievement::class);
    }

    /**
     * Calculate achievement percentage.
     */
    public function calculateAchievementPercentage(): float
    {
        if ($this->target_value == 0) {
            return 0;
        }

        return round(($this->achieved_value / $this->target_value) * 100, 2);
    }

    /**
     * Update achievement values.
     */
    public function updateAchievement(): void
    {
        $totalAchieved = $this->achievements()
            ->where('achievement_type', $this->target_type)
            ->sum('achievement_value');

        $this->update([
            'achieved_value' => $totalAchieved,
            'achievement_percentage' => $this->calculateAchievementPercentage(),
        ]);
    }

    /**
     * Get period display name.
     */
    public function getPeriodDisplayAttribute(): string
    {
        switch ($this->period_type) {
            case 'quarterly':
                return "Q{$this->quarter} {$this->financial_year}";
            case 'monthly':
                $monthNames = [
                    1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
                    5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
                    9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
                ];
                return $monthNames[$this->month] . ' ' . $this->financial_year;
            default:
                return $this->financial_year;
        }
    }

    /**
     * Scope for active targets.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for specific financial year.
     */
    public function scopeForFinancialYear($query, $year)
    {
        return $query->where('financial_year', $year);
    }

    /**
     * Scope for specific period type.
     */
    public function scopeForPeriodType($query, $type)
    {
        return $query->where('period_type', $type);
    }

    /**
     * Scope for specific target type.
     */
    public function scopeForTargetType($query, $type)
    {
        return $query->where('target_type', $type);
    }
}
