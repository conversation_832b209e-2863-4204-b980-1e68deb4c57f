{"__meta": {"id": "X37f56664b6d401b895443f92353c1797", "datetime": "2025-04-16 01:37:39", "utime": **********.399455, "method": "GET", "uri": "/admin/dashboard/stats?type=top-persons", "ip": "127.0.0.1"}, "php": {"version": "8.2.25", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[01:37:39] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$app is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.32362, "xdebug_link": null, "collector": "log"}, {"message": "[01:37:39] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$request is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 22", "message_html": null, "is_string": false, "label": "warning", "time": **********.323796, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1744747658.812379, "end": **********.39948, "duration": 0.5871012210845947, "duration_str": "587ms", "measures": [{"label": "Booting", "start": 1744747658.812379, "relative_start": 0, "end": **********.231682, "relative_end": **********.231682, "duration": 0.41930317878723145, "duration_str": "419ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.231696, "relative_start": 0.41931700706481934, "end": **********.399482, "relative_end": 1.9073486328125e-06, "duration": 0.1677861213684082, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 27746848, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/dashboard/stats", "middleware": "web, admin_locale, user", "controller": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats", "namespace": null, "prefix": "admin/dashboard", "where": [], "as": "admin.dashboard.stats", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=50\" onclick=\"\">packages/Webkul/Admin/src/Http/Controllers/DashboardController.php:50-58</a>"}, "queries": {"nb_statements": 12, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04154, "accumulated_duration_str": "41.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Lead.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Lead.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 963}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.275824, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Lead.php:35", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Lead.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Lead.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FLead.php&line=35", "ajax": false, "filename": "Lead.php", "line": "35"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select `id` from `lead_pipeline_stages` where `code` = 'won'", "type": "query", "params": [], "bindings": ["won"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Lead.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Lead.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 963}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.281304, "duration": 0.02061, "duration_str": "20.61ms", "memory": 0, "memory_str": null, "filename": "Lead.php:35", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Lead.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Lead.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FLead.php&line=35", "ajax": false, "filename": "Lead.php", "line": "35"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 49.615}, {"sql": "select `id` from `lead_pipeline_stages` where `code` = 'lost'", "type": "query", "params": [], "bindings": ["lost"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Lead.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Lead.php", "line": 37}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 963}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.3062322, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Lead.php:37", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Lead.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Lead.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FLead.php&line=37", "ajax": false, "filename": "Lead.php", "line": "37"}, "connection": "laravel-crm", "explain": null, "start_percent": 49.615, "width_percent": 3.635}, {"sql": "select * from `core_config` where `code` = 'general.general.locale_settings.locale'", "type": "query", "params": [], "bindings": ["general.general.locale_settings.locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Core.php", "line": 241}], "start": **********.325735, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 53.25, "width_percent": 1.156}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.339405, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 54.405, "width_percent": 2.455}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, {"index": 22, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "admin_locale", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.345612, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "user:56", "source": {"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=56", "ajax": false, "filename": "Bouncer.php", "line": "56"}, "connection": "laravel-crm", "explain": null, "start_percent": 56.861, "width_percent": 1.059}, {"sql": "select *, `persons`.`id` as `id`, SUM(leads.lead_value) as revenue from `persons` left join `leads` on `persons`.`id` = `leads`.`person_id` where `leads`.`closed_at` between '2025-03-17 00:00:00' and '2025-04-16 01:37:39' group by `person_id` having SUM(leads.lead_value) > 0 order by `revenue` desc limit 5", "type": "query", "params": [], "bindings": ["2025-03-17 00:00:00", "2025-04-16 01:37:39", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Person.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Person.php", "line": 66}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 112}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 52}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.349002, "duration": 0.0134, "duration_str": "13.4ms", "memory": 0, "memory_str": null, "filename": "Person.php:66", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Person.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Person.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FPerson.php&line=66", "ajax": false, "filename": "Person.php", "line": "66"}, "connection": "laravel-crm", "explain": null, "start_percent": 57.92, "width_percent": 32.258}, {"sql": "select * from `attributes` where `code` = 'organization_id'", "type": "query", "params": [], "bindings": ["organization_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 23}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 109}, {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 51}], "start": **********.365837, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "laravel-crm", "explain": null, "start_percent": 90.178, "width_percent": 1.974}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 5 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Person.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Person.php", "line": 66}, {"index": 31, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 112}], "start": **********.3727162, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 92.152, "width_percent": 2.817}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 15 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Person.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Person.php", "line": 66}, {"index": 31, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 112}], "start": **********.376676, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 94.969, "width_percent": 1.3}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 2 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Person.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Person.php", "line": 66}, {"index": 31, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 112}], "start": **********.37959, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 96.269, "width_percent": 1.204}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 1 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Person.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Person.php", "line": 66}, {"index": 31, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 112}], "start": **********.382798, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 97.472, "width_percent": 0.987}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 7 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Person.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Person.php", "line": 66}, {"index": 31, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 112}], "start": **********.3852332, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 98.459, "width_percent": 1.541}]}, "models": {"data": {"Webkul\\Contact\\Models\\Person": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FContact%2Fsrc%2FModels%2FPerson.php&line=1", "ajax": false, "filename": "Person.php", "line": "?"}}, "Webkul\\Lead\\Models\\Stage": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FLead%2Fsrc%2FModels%2FStage.php&line=1", "ajax": false, "filename": "Stage.php", "line": "?"}}, "Webkul\\User\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/admin/dashboard/stats", "status_code": "<pre class=sf-dump id=sf-dump-1543097463 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1543097463\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1553775466 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">top-persons</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553775466\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2093005293 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2093005293\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdiYWgwMHJ3eUtacndOWlRKQjNkZmc9PSIsInZhbHVlIjoidCthc3FwMDhxRERjSGhid3VJUVgvRjZyQ1EvTldhLzg0UzhWbHZ1QWQvRlhJU2dPb3A3VzlDWHNFZ3ZZTWZDQkxvZGhtbTYrc2lvUkFIWERKTFZkZUhNVnd0Q0dqWnlsd3hqY3lEWVN5K1VmUzBHREFPcE4ybEw2OUlVWDZIdGQiLCJtYWMiOiIzZTBlNTczMGNlMjJlNDg0NmY5MTZhOGRjMGEyMjA0OWUxYWUyOGUyN2FkMzNkNmI2ZmNlNDE4ZjY0NTk5M2RmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Brave&quot;;v=&quot;135&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"729 characters\">dark_mode=0; XSRF-TOKEN=eyJpdiI6IkdiYWgwMHJ3eUtacndOWlRKQjNkZmc9PSIsInZhbHVlIjoidCthc3FwMDhxRERjSGhid3VJUVgvRjZyQ1EvTldhLzg0UzhWbHZ1QWQvRlhJU2dPb3A3VzlDWHNFZ3ZZTWZDQkxvZGhtbTYrc2lvUkFIWERKTFZkZUhNVnd0Q0dqWnlsd3hqY3lEWVN5K1VmUzBHREFPcE4ybEw2OUlVWDZIdGQiLCJtYWMiOiIzZTBlNTczMGNlMjJlNDg0NmY5MTZhOGRjMGEyMjA0OWUxYWUyOGUyN2FkMzNkNmI2ZmNlNDE4ZjY0NTk5M2RmIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6Inl0QUhIRE5mQytJblVBbHA0OVhNN2c9PSIsInZhbHVlIjoicWZRU2h0bm1tK0FqWUd0cnIwL0xteGM5Zmh0NmdEblA3MzEwNGlIeHFVS3c4dVU1WXo1cE9jU2YvN2MvUlpmaWJuYVdLVjVWNTdjcU9MSGtVbkhQdyt4bzVybkphR2FYOWhjQ0VsWTVrNGFxZStPclhqVkNINkpyeGtkdE9ERHQiLCJtYWMiOiIxZTMyNGNiOWM0ZTAxZDUxY2I1N2U3ZTljZDY4MGZmNTE2M2Q0OGRmODdiNjgxYjliZTIxMjRhOTAyMDdjNjNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lh4xOQcW5AWaSgG8tSFW592msyWyo1WTTiE9zQSW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-89595369 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Apr 2025 20:07:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkJ2K1R1Qld6dWxQVXptRUhuMDIxdHc9PSIsInZhbHVlIjoibXhoVlkwdVFDc0VvZDZ3VTYyM0xzeEJqWERnSFNiYytIOWZ5NmVzV3U0K1I2QmpzNlZNN0wrdW0wUjV0TUtYRGlVdCtnL0JnQUZhdDR5VElhOENudzFsOGhpUDVZUk9PbzBNSEJKSTRORkx2UUdSZ2VMUTBxZmp0bGJkZmR1VWMiLCJtYWMiOiJjMjQ1Y2Y5YjM3ZGFkZjU1NTA2YzFjZmI1OTkzYTlmZjI0MTE5NjIyZGE0NWMxYjc3NjQ0NWY0OWVmODAxZDE0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 22:07:39 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">krayin_crm_session=eyJpdiI6ImNId25MaXp1RVJaZjJFN2V0UW1wenc9PSIsInZhbHVlIjoiVnA5UWVtRTdmaExmc0NHL29LZU5NQnhoRUN1YVg1dFdvUHBTQUJXVXNVWHRKV1RQS2pJVFU1YytYeGQ1OExDOEFZckVFTm1veFZ6WXVBWkVqWGpoSFcyeklTMFNZeHorVmg3Y3FUZEtyaHIrWFJueGJSNDFzUjlGVUNvaEp3bjciLCJtYWMiOiJjMGUwNjFkNmUwMjBlZDlkZWYwZDEwNmM4YzU1ZTAzMDdiYjJhOTk2MzNjNjZjNzc4MjMxOWI5MTUwYjUxOWFmIiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 22:07:39 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkJ2K1R1Qld6dWxQVXptRUhuMDIxdHc9PSIsInZhbHVlIjoibXhoVlkwdVFDc0VvZDZ3VTYyM0xzeEJqWERnSFNiYytIOWZ5NmVzV3U0K1I2QmpzNlZNN0wrdW0wUjV0TUtYRGlVdCtnL0JnQUZhdDR5VElhOENudzFsOGhpUDVZUk9PbzBNSEJKSTRORkx2UUdSZ2VMUTBxZmp0bGJkZmR1VWMiLCJtYWMiOiJjMjQ1Y2Y5YjM3ZGFkZjU1NTA2YzFjZmI1OTkzYTlmZjI0MTE5NjIyZGE0NWMxYjc3NjQ0NWY0OWVmODAxZDE0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 22:07:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">krayin_crm_session=eyJpdiI6ImNId25MaXp1RVJaZjJFN2V0UW1wenc9PSIsInZhbHVlIjoiVnA5UWVtRTdmaExmc0NHL29LZU5NQnhoRUN1YVg1dFdvUHBTQUJXVXNVWHRKV1RQS2pJVFU1YytYeGQ1OExDOEFZckVFTm1veFZ6WXVBWkVqWGpoSFcyeklTMFNZeHorVmg3Y3FUZEtyaHIrWFJueGJSNDFzUjlGVUNvaEp3bjciLCJtYWMiOiJjMGUwNjFkNmUwMjBlZDlkZWYwZDEwNmM4YzU1ZTAzMDdiYjJhOTk2MzNjNjZjNzc4MjMxOWI5MTUwYjUxOWFmIiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 22:07:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89595369\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-854331323 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-854331323\", {\"maxDepth\":0})</script>\n"}}