name: CI

on: [push, pull_request]

jobs:
  tests:
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: [ubuntu-latest]
        php-versions: ["8.1"]
    name: PHP ${{ matrix.php-versions }} test on ${{ matrix.operating-system }}

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: krayin
        ports:
          - 3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=5

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: curl, gd, intl, mbstring, openssl, pdo, pdo_mysql, tokenizer, zip

      - name: Composer Install
        run: composer install

      - name: Set Testing Environment
        run: |
          cp .env.example .env
          sed -i "s|^\(APP_ENV=\s*\).*$|\1testing|" .env
          sed -i "s|^\(DB_HOST=\s*\).*$|\1127.0.0.1|" .env
          sed -i "s|^\(DB_PORT=\s*\).*$|\1${{ job.services.mysql.ports['3306'] }}|" .env
          sed -i "s|^\(DB_DATABASE=\s*\).*$|\1krayin|" .env
          sed -i "s|^\(DB_USERNAME=\s*\).*$|\1root|" .env
          sed -i "s|^\(DB_PASSWORD=\s*\).*$|\1root|" .env

      - name: Key Generate
        run: php artisan key:generate

      - name: Complete ENV File
        run: |
          printf "The complete `.env` ... \n\n"
          cat .env

      - name: Migrate Database
        run: php artisan migrate

      - name: Seed Database
        run: php artisan db:seed

      - name: Vendor Publish
        run: php artisan vendor:publish --provider=Webkul\\Core\\Providers\\CoreServiceProvider --force

      - name: Optimize Stuffs
        run: php artisan optimize:clear

      - name: Run Tests
        run: vendor/bin/pest
