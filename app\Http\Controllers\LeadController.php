<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Http;

class LeadController extends Controller
{
    public function bulkUpload(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xls,xlsx',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Validation failed.', 'errors' => $validator->errors()], 422);
        }

        // Read the Excel file
        $file = $request->file('file');
        $rows = Excel::toArray([], $file)[0]; // Extract rows from the first sheet

        // Ensure the file has headers and data
        if (empty($rows) || count($rows) < 2) {
            return response()->json(['message' => 'Invalid file format or no data found.'], 400);
        }

        // Extract headers and map them to Krayin CRM fields
        $headers = $rows[0];
        $leads = array_slice($rows, 1); // Exclude the header row

        // Prepare the data for Krayin CRM
        $mappedLeads = [];
        foreach ($leads as $row) {
            $mappedLeads[] = array_combine($headers, $row);
        }

        try {
            // Call Krayin CRM's API to create leads
            $response = Http::withHeaders([
                'Authorization' => 'Bearer YOUR_ACCESS_TOKEN',
                'Content-Type' => 'application/json',
            ])->post('https://your-krayin-crm-api-endpoint.com/api/leads/bulk-create', [
                'leads' => $this->formatLeadsForKrayin($mappedLeads),
            ]);

            if ($response->successful()) {
                return response()->json(['message' => 'Leads uploaded successfully.', 'data' => $response->json()]);
            }

            return response()->json(['message' => 'Failed to upload leads.', 'error' => $response->body()], $response->status());
        } catch (\Exception $e) {
            return response()->json(['message' => 'An error occurred.', 'error' => $e->getMessage()], 500);
        }
    }

    private function formatLeadsForKrayin($leads)
    {
        $formattedLeads = [];

        foreach ($leads as $lead) {
            // Map the fields to Krayin CRM's expected structure
            $formattedLeads[] = [
                'title' => $lead['title'],
                'description' => $lead['description'],
                'source' => $lead['source'],
                'type' => $lead['type'],
                'expected_close_date' => $lead['expected_close_date'],
                'sales_owner' => $lead['sales_owner'],
                'lead_value' => $lead['lead_value'],
                'contact_person' => [
                    'name' => $lead['contact_name'],
                    'email' => $lead['contact_email'],
                    'phone' => $lead['contact_number'],
                    'organization' => $lead['organization'],
                ],
                'products' => [
                    [
                        'name' => $lead['product_name'],
                        'quantity' => $lead['quantity'],
                        'price' => $lead['price'],
                        'amount' => $lead['amount'],
                    ],
                ],
                'custom_attributes' => [
                    [
                        'attribute_name' => 'lead_received',
                        'attribute_value' => $lead['lead_received'], // Custom attribute
                    ],
                ],
            ];
        }

        return $formattedLeads;
    }
}