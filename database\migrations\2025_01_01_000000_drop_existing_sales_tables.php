<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('sales_team_members');
        Schema::dropIfExists('sales_achievements');
        Schema::dropIfExists('sales_teams');
        Schema::dropIfExists('sales_targets');
        Schema::dropIfExists('crm_sales_team_members');
        Schema::dropIfExists('crm_sales_achievements');
        Schema::dropIfExists('crm_sales_teams');
        Schema::dropIfExists('crm_sales_targets');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // No need to recreate tables as they will be created by the proper migrations
    }
};
