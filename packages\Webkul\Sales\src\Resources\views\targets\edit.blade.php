@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.targets.edit') }}
@stop

@section('content')
    <div class="content">
        <div class="flex justify-between items-center mt-[10px] max-sm:flex-wrap">
            <div class="flex flex-col gap-[8px]">
                <h1>{{ __('sales::app.targets.edit') }}</h1>
                <p>{{ __('sales::app.targets.edit-description') }}</p>
            </div>
            
            <div class="flex gap-x-[10px] items-center">
                <a href="{{ route('admin.sales.targets.index') }}" class="transparent-button hover:bg-gray-200 dark:hover:bg-gray-800 dark:text-white">
                    {{ __('sales::app.targets.edit.back-btn') }}
                </a>

                <button type="submit" class="primary-button">
                    {{ __('sales::app.targets.edit.save-btn') }}
                </button>
            </div>
        </div>

        <x-admin::form
            :action="route('admin.sales.targets.update', $target->id)"
            method="PUT"
        >
            <div class="flex flex-col gap-[8px] w-full mt-[28px]">
                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.edit.user') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="select"
                        name="user_id"
                        :value="old('user_id', $target->user_id)"
                        :options="$users->pluck('name', 'id')"
                        rules="required"
                        :label="__('sales::app.targets.edit.user')"
                        :placeholder="__('sales::app.targets.edit.select-user')"
                    />

                    <x-admin::form.control-group.error control-name="user_id" />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.edit.financial-year') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="select"
                        name="financial_year"
                        :value="old('financial_year', $target->financial_year)"
                        :options="$financialYears"
                        rules="required"
                        :label="__('sales::app.targets.edit.financial-year')"
                        :placeholder="__('sales::app.targets.edit.select-financial-year')"
                    />

                    <x-admin::form.control-group.error control-name="financial_year" />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.edit.period-type') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="select"
                        name="period_type"
                        :value="old('period_type', $target->period_type)"
                        :options="[
                            ['value' => 'annual', 'label' => __('sales::app.targets.edit.annual')],
                            ['value' => 'half_yearly', 'label' => __('sales::app.targets.edit.half-yearly')],
                            ['value' => 'quarterly', 'label' => __('sales::app.targets.edit.quarterly')],
                            ['value' => 'monthly', 'label' => __('sales::app.targets.edit.monthly')],
                            ['value' => 'custom', 'label' => __('sales::app.targets.edit.custom')],
                        ]"
                        rules="required"
                        :label="__('sales::app.targets.edit.period-type')"
                        :placeholder="__('sales::app.targets.edit.select-period-type')"
                    />

                    <x-admin::form.control-group.error control-name="period_type" />
                </x-admin::form.control-group>

                <div id="period_value_container">
                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.edit.period-value') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="select"
                            name="period_value"
                            :value="old('period_value', $target->period_value)"
                            :options="[]"
                            :label="__('sales::app.targets.edit.period-value')"
                            :placeholder="__('sales::app.targets.edit.select-period')"
                        />

                        <x-admin::form.control-group.error control-name="period_value" />
                    </x-admin::form.control-group>
                </div>

                <div id="custom_date_range_container" class="flex flex-col gap-[8px]" style="display: none;">
                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.edit.start-date') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="date"
                            name="start_date"
                            :value="old('start_date', $target->start_date)"
                            :label="__('sales::app.targets.edit.start-date')"
                        />

                        <x-admin::form.control-group.error control-name="start_date" />
                    </x-admin::form.control-group>

                    <x-admin::form.control-group>
                        <x-admin::form.control-group.label class="required">
                            {{ __('sales::app.targets.edit.end-date') }}
                        </x-admin::form.control-group.label>

                        <x-admin::form.control
                            type="date"
                            name="end_date"
                            :value="old('end_date', $target->end_date)"
                            :label="__('sales::app.targets.edit.end-date')"
                        />

                        <x-admin::form.control-group.error control-name="end_date" />
                    </x-admin::form.control-group>
                </div>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.edit.target-value') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="text"
                        name="target_value"
                        :value="old('target_value', $target->target_value)"
                        rules="required|numeric"
                        :label="__('sales::app.targets.edit.target-value')"
                    />

                    <x-admin::form.control-group.error control-name="target_value" />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label class="required">
                        {{ __('sales::app.targets.edit.target-type') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="select"
                        name="target_type"
                        :value="old('target_type', $target->target_type)"
                        :options="[
                            ['value' => 'revenue', 'label' => __('sales::app.targets.edit.revenue')],
                            ['value' => 'deals', 'label' => __('sales::app.targets.edit.deals')],
                            ['value' => 'leads', 'label' => __('sales::app.targets.edit.leads')],
                        ]"
                        rules="required"
                        :label="__('sales::app.targets.edit.target-type')"
                    />

                    <x-admin::form.control-group.error control-name="target_type" />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control-group.label>
                        {{ __('sales::app.targets.edit.description') }}
                    </x-admin::form.control-group.label>

                    <x-admin::form.control
                        type="textarea"
                        name="description"
                        :value="old('description', $target->description)"
                        :label="__('sales::app.targets.edit.description')"
                    />
                </x-admin::form.control-group>

                <x-admin::form.control-group>
                    <x-admin::form.control
                        type="checkbox"
                        name="is_active"
                        :value="1"
                        :checked="old('is_active', $target->is_active)"
                        :label="__('sales::app.targets.edit.is-active')"
                    />
                </x-admin::form.control-group>
            </div>
        </x-admin::form>
    </div>
@stop

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const periodTypeSelect = document.getElementById('period_type');
    const periodValueContainer = document.getElementById('period_value_container');
    const periodValueSelect = document.getElementById('period_value');
    const customDateRangeContainer = document.getElementById('custom_date_range_container');
    const customDateRangeContainerEnd = document.getElementById('custom_date_range_container_end');
    const currentPeriodValue = '{{ old("period_value", $target->period_value) }}';

    function updatePeriodValues() {
        const periodType = periodTypeSelect.value;
        periodValueSelect.innerHTML = '<option value="">Select Period</option>';

        if (periodType === 'custom') {
            periodValueContainer.style.display = 'none';
            customDateRangeContainer.style.display = 'flex';
            customDateRangeContainerEnd.style.display = 'flex';
            periodValueSelect.required = false;
            document.getElementById('start_date').required = true;
            document.getElementById('end_date').required = true;
        } else {
            periodValueContainer.style.display = 'block';
            customDateRangeContainer.style.display = 'none';
            customDateRangeContainerEnd.style.display = 'none';
            periodValueSelect.required = true;
            document.getElementById('start_date').required = false;
            document.getElementById('end_date').required = false;
        }

        if (periodType === 'annual') {
            periodValueContainer.style.display = 'none';
            periodValueSelect.required = false;
        } else if (periodType === 'half_yearly') {
            for (let i = 1; i <= 2; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `H${i}`;
                if (currentPeriodValue == i) option.selected = true;
                periodValueSelect.appendChild(option);
            }
        } else if (periodType === 'quarterly') {
            for (let i = 1; i <= 4; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `Q${i}`;
                if (currentPeriodValue == i) option.selected = true;
                periodValueSelect.appendChild(option);
            }
        } else if (periodType === 'monthly') {
            const months = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];
            months.forEach((month, index) => {
                const option = document.createElement('option');
                option.value = index + 1;
                option.textContent = month;
                if (currentPeriodValue == (index + 1)) option.selected = true;
                periodValueSelect.appendChild(option);
            });
        }
    }
    
    periodTypeSelect.addEventListener('change', updatePeriodValues);
    
    // Initialize on page load
    if (periodTypeSelect.value) {
        updatePeriodValues();
    }
});
</script>
@endpush


@push('css')
<style>
    .required::after {
        content: " *";
        color: red;
    }
    
    .form-actions {
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }
    
    .form-actions .btn {
        margin-right: 0.5rem;
    }
</style>
@endpush
