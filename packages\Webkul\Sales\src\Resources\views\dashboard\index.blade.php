@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.dashboard.title') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.dashboard.title') }}</h1>
                <p>{{ __('sales::app.dashboard.subtitle') }}</p>
            </div>

            <div class="page-action">
                <div class="form-group">
                    <select class="control" id="financial-year-filter" onchange="filterByYear(this.value)">
                        @foreach($financialYears as $year)
                            <option value="{{ $year }}" {{ $year == $selectedYear ? 'selected' : '' }}>
                                {{ $year }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="icon-target"></i>
                            </div>
                            <div class="stat-content">
                                <h3>{{ $stats['total_targets'] }}</h3>
                                <p>{{ __('sales::app.dashboard.total-targets') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="icon-currency"></i>
                            </div>
                            <div class="stat-content">
                                <h3>{{ core()->formatPrice($stats['total_target_value']) }}</h3>
                                <p>{{ __('sales::app.dashboard.total-target-value') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="icon-achievement"></i>
                            </div>
                            <div class="stat-content">
                                <h3>{{ core()->formatPrice($stats['total_achieved_value']) }}</h3>
                                <p>{{ __('sales::app.dashboard.total-achieved-value') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="icon-percentage"></i>
                            </div>
                            <div class="stat-content">
                                <h3>{{ $stats['avg_achievement_percentage'] }}%</h3>
                                <p>{{ __('sales::app.dashboard.avg-achievement') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Performance -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>{{ __('sales::app.dashboard.performance-trends') }}</h4>
                    </div>
                    <div class="card-body">
                        <canvas id="performanceChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h4>{{ __('sales::app.dashboard.top-performers') }}</h4>
                    </div>
                    <div class="card-body">
                        <div class="leaderboard">
                            @foreach($topPerformers->take(5) as $index => $performer)
                                <div class="leaderboard-item">
                                    <div class="rank">{{ $index + 1 }}</div>
                                    <div class="user-info">
                                        <div class="name">{{ $performer->user->name }}</div>
                                        <div class="achievement">{{ $performer->achievement_percentage }}%</div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: {{ min($performer->achievement_percentage, 100) }}%"></div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Achievements -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4>{{ __('sales::app.dashboard.recent-achievements') }}</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>{{ __('sales::app.dashboard.user') }}</th>
                                        <th>{{ __('sales::app.dashboard.achievement-type') }}</th>
                                        <th>{{ __('sales::app.dashboard.value') }}</th>
                                        <th>{{ __('sales::app.dashboard.date') }}</th>
                                        <th>{{ __('sales::app.dashboard.description') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentAchievements as $achievement)
                                        <tr>
                                            <td>{{ $achievement->user->name }}</td>
                                            <td>
                                                <span class="badge badge-{{ $achievement->achievement_type == 'revenue' ? 'success' : ($achievement->achievement_type == 'leads' ? 'info' : 'warning') }}">
                                                    {{ ucfirst($achievement->achievement_type) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($achievement->achievement_type == 'revenue')
                                                    {{ core()->formatPrice($achievement->achievement_value) }}
                                                @else
                                                    {{ $achievement->achievement_value }}
                                                @endif
                                            </td>
                                            <td>{{ $achievement->achievement_date->format('M d, Y') }}</td>
                                            <td>{{ $achievement->description ?? '-' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        function filterByYear(year) {
            window.location.href = "{{ route('admin.sales.index') }}?financial_year=" + year;
        }

        // Performance Chart
        const ctx = document.getElementById('performanceChart').getContext('2d');
        const performanceData = @json($performanceTrends);
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                datasets: [{
                    label: 'Target',
                    data: Object.values(performanceData.quarterly).map(q => q.target),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: 'Achieved',
                    data: Object.values(performanceData.quarterly).map(q => q.achieved),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
@endpush

@push('css')
    <style>
        .stat-card {
            display: flex;
            align-items: center;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 24px;
        }
        
        .stat-content h3 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        
        .stat-content p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .leaderboard-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .leaderboard-item:last-child {
            border-bottom: none;
        }
        
        .rank {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-info .name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .user-info .achievement {
            font-size: 12px;
            color: #666;
        }
        
        .progress {
            width: 100px;
            height: 6px;
            background: #eee;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: #28a745;
            transition: width 0.3s ease;
        }
    </style>
@endpush
