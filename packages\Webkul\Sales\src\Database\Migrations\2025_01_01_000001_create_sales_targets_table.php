<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('crm_sales_targets', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->unsigned();
            $table->string('financial_year', 10); // e.g., '2024-25'
            $table->enum('period_type', ['annual', 'quarterly', 'monthly'])->default('annual');
            $table->tinyInteger('quarter')->nullable(); // 1, 2, 3, 4
            $table->tinyInteger('month')->nullable(); // 1-12
            $table->enum('target_type', ['revenue', 'leads', 'deals'])->default('revenue');
            $table->decimal('target_value', 15, 2);
            $table->decimal('achieved_value', 15, 2)->default(0);
            $table->decimal('achievement_percentage', 5, 2)->default(0);
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status', ['active', 'inactive', 'completed'])->default('active');
            $table->text('notes')->nullable();
            $table->integer('created_by')->unsigned();
            $table->integer('updated_by')->unsigned()->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');

            // Ensure unique targets per user per period
            $table->unique(['user_id', 'financial_year', 'period_type', 'quarter', 'month', 'target_type'], 'unique_user_period_target');

            $table->index(['user_id', 'financial_year']);
            $table->index(['period_type', 'target_type']);
            $table->index(['start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('crm_sales_targets');
    }
};
