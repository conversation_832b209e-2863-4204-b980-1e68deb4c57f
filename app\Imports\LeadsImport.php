<?php

namespace App\Imports;

use App\Models\Lead; // Add Log facade
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow; // Add SkipsOnError
use Maatwebsite\Excel\Concerns\WithValidation;
use Throwable;
use Webkul\Contact\Models\Person; // Import models
use Webkul\Lead\Models\LeadPipelineStage;
use Webkul\Lead\Models\LeadSource;
use Webkul\User\Models\User;

// Implement SkipsOnError to allow skipping rows with errors
class LeadsImport implements SkipsOnError, ToModel, WithHeadingRow, WithValidation
{
    /**
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        Log::info('Processing row:', $row); // Add this line
        try {
            // Basic validation example (can be expanded)
            if (empty($row['title'])) {
                Log::warning('Skipping row due to missing title:', $row);

                return null; // Skip this row
            }

            // Map your columns as needed
            return new Lead([
                'title'                 => $row['title'] ?? null,
                'description'           => $row['description'] ?? null,
                'lead_value'            => $row['lead_value'] ?? null,
                // Assuming you have ways to map names to IDs or expect IDs directly
                // These might need more robust lookup logic depending on your CSV format
                'lead_pipeline_stage_id' => $this->getStageId($row['stage_name'] ?? null), // Example: Map stage name to ID
                'lead_source_id'         => $this->getSourceId($row['source_name'] ?? null), // Example: Map source name to ID
                'user_id'                => $this->getUserId($row['user_name'] ?? null), // Example: Map user name to ID
                'person_id'              => $this->getPersonId($row['person_name'] ?? null), // Example: Map person name to ID
                'expected_close_date'    => ! empty($row['expected_close_date']) ? \Carbon\Carbon::parse($row['expected_close_date'])->format('Y-m-d') : null, // Example date parsing
                // Add other relevant fields from your Lead model and CSV
                // 'type_id' => $row['type_id'] ?? null, // Assuming type_id exists
                // 'status' => $row['status'] ?? 1, // Default status if needed
            ]);
        } catch (Throwable $e) {
            // Log the error and the row data that caused it
            Log::error('Error processing row during lead import: '.$e->getMessage(), ['row' => $row]);

            // Optionally rethrow or handle specific exceptions
            // throw $e; // Re-throw if you want the import to fail completely
            return null; // Skip this row on generic error
        }
    }

    /**
     * Handle errors during import.
     *
     * @return void
     */
    public function onError(Throwable $e)
    {
        // Log the error. The row data might not be available here depending on where the error occurred.
        Log::error('Lead Import Error: '.$e->getMessage());
        // This method is part of SkipsOnError.
        // The row causing the error will be skipped automatically.
    }

    // --- Helper functions to map names to IDs (Implement these based on your models) ---

    protected function getStageId($stageName)
    {
        if (! $stageName) {
            return null;
        }
        $trimmedStageName = trim($stageName);
        if (empty($trimmedStageName)) {
            return null;
        }

        // Find Stage ID by name, case-insensitive
        $stage = LeadPipelineStage::whereRaw('LOWER(name) = ?', [strtolower($trimmedStageName)])->first();

        if (! $stage) {
            // Decide if you want to create stages on the fly. Usually not recommended.
            // For now, just log a warning and return null.
            Log::warning('Lead Stage not found during import for name: '.$stageName.'. Skipping stage assignment for this lead.');

            return null;
        }

        return $stage->id;
    }

    protected function getSourceId($sourceName)
    {
        if (! $sourceName) {
            return null;
        }
        $trimmedSourceName = trim($sourceName);
        if (empty($trimmedSourceName)) {
            return null;
        }

        // Find LeadSource ID by name, ignoring case
        $source = LeadSource::whereRaw('LOWER(name) = ?', [strtolower($trimmedSourceName)])->first();

        // If not found, attempt to create it
        if (! $source) {
            Log::info('[Lead Import] Attempting to create Lead Source: "'.$trimmedSourceName.'"');
            try {
                // Ensure the name field is fillable in the LeadSource model
                $source = LeadSource::create(['name' => $trimmedSourceName]);
                Log::info('[Lead Import] Successfully created Lead Source ID: '.$source->id.' for name: "'.$trimmedSourceName.'"');
            } catch (Throwable $e) {
                // Log detailed error including potential validation issues or DB constraints
                Log::error('[Lead Import] Failed to create Lead Source "'.$trimmedSourceName.'". Error: '.$e->getMessage(), [
                    'exception'  => $e,
                    'sourceName' => $sourceName,
                ]);

                return null; // Return null if creation fails
            }
        }

        return $source ? $source->id : null; // Return ID or null
    }

    protected function getUserId($userName)
    {
        if (! $userName) {
            return null;
        }
        $trimmedUserName = trim($userName);
        if (empty($trimmedUserName)) {
            return null;
        }

        // Find User ID by name (case-insensitive)
        $user = User::whereRaw('LOWER(name) = ?', [strtolower($trimmedUserName)])->first();

        // Fallback to finding by email (case-insensitive)
        if (! $user) {
            $user = User::whereRaw('LOWER(email) = ?', [strtolower($trimmedUserName)])->first();
        }

        if (! $user) {
            Log::warning('User not found during import for name/email: '.$userName.'. Skipping user assignment.');
        }

        return $user ? $user->id : null;
    }

    protected function getPersonId($personName)
    {
        if (! $personName) {
            return null;
        }
        $trimmedPersonName = trim($personName);
        if (empty($trimmedPersonName)) {
            return null;
        }

        // Find Person ID by name (case-insensitive)
        // Consider adding email matching if needed for uniqueness
        $person = Person::whereRaw('LOWER(name) = ?', [strtolower($trimmedPersonName)])->first();

        // Optional: Create person if not found (uncomment and adjust if needed)
        /*
        if (!$person) {
            Log::info('Person not found during import, creating new person: ' . $trimmedPersonName);
            try {
                // Ensure you provide all required fields for Person creation
                $person = Person::create([
                    'name' => $trimmedPersonName,
                    // 'email' => $row['person_email'] ?? null, // Example: Get email from another column if available
                    // Add other required fields for your Person model
                ]);
            } catch (Throwable $e) {
                Log::error('Failed to create Person "' . $trimmedPersonName . '" during import: ' . $e->getMessage());
                return null; // Return null if creation fails
            }
        }
        */

        if (! $person) {
            Log::warning('Person not found during import for name: '.$personName.'. Skipping person assignment.');
        }

        return $person ? $person->id : null;
    }

    /**
     * Define validation rules for the import.
     */
    public function rules(): array
    {
        return [
            'title'       => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'lead_value'  => ['nullable', 'numeric'],
            'stage_name'  => function ($attribute, $value, $fail) {
                if ($value) {
                    $stage = LeadPipelineStage::whereRaw('LOWER(name) = ?', [strtolower(trim($value))])->first();
                    if (! $stage) {
                        $fail("Lead Stage not found: {$value}");
                    }
                }
            },
            'source_name' => function ($attribute, $value, $fail) {
                if ($value) {
                    $source = LeadSource::whereRaw('LOWER(name) = ?', [strtolower(trim($value))])->first();
                    if (! $source) {
                        $fail("Lead Source not found: {$value}");
                    }
                }
            },
            'user_name' => function ($attribute, $value, $fail) {
                if ($value) {
                    $user = User::whereRaw('LOWER(name) = ?', [strtolower(trim($value))])
                        ->orWhereRaw('LOWER(email) = ?', [strtolower(trim($value))])
                        ->first();

                    if (! $user) {
                        $fail("User not found: {$value}");
                    }
                }
            },
            'person_name' => function ($attribute, $value, $fail) {
                if ($value) {
                    $person = Person::whereRaw('LOWER(name) = ?', [strtolower(trim($value))])->first();
                    if (! $person) {
                        $fail("Person not found: {$value}");
                    }
                }
            },
            'expected_close_date' => ['nullable', 'date_format:Y-m-d'],
        ];
    }
}
