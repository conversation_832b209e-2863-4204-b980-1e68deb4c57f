<?php
    $lookUpEntity = app('Webkul\Attribute\Repositories\AttributeRepository')->getLookUpEntity($attribute->lookup_type, $value);
?>

<?php if (isset($component)) { $__componentOriginal21fb926d55e060440adffb0991b1ce00 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal21fb926d55e060440adffb0991b1ce00 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.controls.inline.lookup','data' => [':name' => '\''.e($attribute->code).'\'',':value' => '\''.e($lookUpEntity?->name).'\'','attribute' => $attribute,'position' => 'left','label' => $attribute->name,':errors' => 'errors','placeholder' => $attribute->name,'url' => $url,'allowEdit' => $allowEdit]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.controls.inline.lookup'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':name' => '\''.e($attribute->code).'\'',':value' => '\''.e($lookUpEntity?->name).'\'','attribute' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attribute),'position' => 'left','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attribute->name),':errors' => 'errors','placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attribute->name),'url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($url),'allow-edit' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allowEdit)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal21fb926d55e060440adffb0991b1ce00)): ?>
<?php $attributes = $__attributesOriginal21fb926d55e060440adffb0991b1ce00; ?>
<?php unset($__attributesOriginal21fb926d55e060440adffb0991b1ce00); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal21fb926d55e060440adffb0991b1ce00)): ?>
<?php $component = $__componentOriginal21fb926d55e060440adffb0991b1ce00; ?>
<?php unset($__componentOriginal21fb926d55e060440adffb0991b1ce00); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/components/attributes/view/lookup.blade.php ENDPATH**/ ?>