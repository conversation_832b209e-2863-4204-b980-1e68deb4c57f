@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.targets.edit') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.targets.edit') }}</h1>
                <p>{{ __('sales::app.targets.edit-description') }}</p>
            </div>
            
            <div class="page-action">
                <a href="{{ route('admin.sales.targets.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Targets
                </a>
            </div>
        </div>

        <div class="page-content">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.sales.targets.update', $target->id) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="user_id" class="required">Sales Representative</label>
                                    <select name="user_id" id="user_id" class="form-control @error('user_id') is-invalid @enderror" required>
                                        <option value="">Select Sales Representative</option>
                                        @if(isset($users))
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}" {{ old('user_id', $target->user_id) == $user->id ? 'selected' : '' }}>
                                                    {{ $user->name }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @error('user_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="financial_year" class="required">Financial Year</label>
                                    <select name="financial_year" id="financial_year" class="form-control @error('financial_year') is-invalid @enderror" required>
                                        <option value="">Select Financial Year</option>
                                        @for($year = date('Y') - 2; $year <= date('Y') + 2; $year++)
                                            <option value="{{ $year }}" {{ old('financial_year', $target->financial_year) == $year ? 'selected' : '' }}>
                                                {{ $year }}
                                            </option>
                                        @endfor
                                    </select>
                                    @error('financial_year')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="period_type" class="required">Period Type</label>
                                    <select name="period_type" id="period_type" class="form-control @error('period_type') is-invalid @enderror" required>
                                        <option value="">Select Period Type</option>
                                        <option value="quarter" {{ old('period_type', $target->period_type) == 'quarter' ? 'selected' : '' }}>Quarter</option>
                                        <option value="month" {{ old('period_type', $target->period_type) == 'month' ? 'selected' : '' }}>Month</option>
                                        <option value="half_year" {{ old('period_type', $target->period_type) == 'half_year' ? 'selected' : '' }}>Half Year</option>
                                    </select>
                                    @error('period_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="period_value" class="required">Period Value</label>
                                    <select name="period_value" id="period_value" class="form-control @error('period_value') is-invalid @enderror" required>
                                        <option value="">Select Period</option>
                                    </select>
                                    @error('period_value')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="target_amount" class="required">Target Amount</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="number" 
                                               name="target_amount" 
                                               id="target_amount" 
                                               class="form-control @error('target_amount') is-invalid @enderror" 
                                               value="{{ old('target_amount', $target->target_amount) }}" 
                                               step="0.01" 
                                               min="0" 
                                               required>
                                    </div>
                                    @error('target_amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="target_type">Target Type</label>
                                    <select name="target_type" id="target_type" class="form-control @error('target_type') is-invalid @enderror">
                                        <option value="revenue" {{ old('target_type', $target->target_type ?? 'revenue') == 'revenue' ? 'selected' : '' }}>Revenue</option>
                                        <option value="deals" {{ old('target_type', $target->target_type) == 'deals' ? 'selected' : '' }}>Number of Deals</option>
                                        <option value="leads" {{ old('target_type', $target->target_type) == 'leads' ? 'selected' : '' }}>Number of Leads</option>
                                    </select>
                                    @error('target_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea name="description" 
                                      id="description" 
                                      class="form-control @error('description') is-invalid @enderror" 
                                      rows="3" 
                                      placeholder="Optional description for this target">{{ old('description', $target->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" 
                                       name="is_active" 
                                       id="is_active" 
                                       class="form-check-input" 
                                       value="1" 
                                       {{ old('is_active', $target->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active Target
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Target
                            </button>
                            <a href="{{ route('admin.sales.targets.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@stop

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const periodTypeSelect = document.getElementById('period_type');
    const periodValueSelect = document.getElementById('period_value');
    const currentPeriodValue = '{{ old("period_value", $target->period_value) }}';
    
    function updatePeriodValues() {
        const periodType = periodTypeSelect.value;
        periodValueSelect.innerHTML = '<option value="">Select Period</option>';
        
        if (periodType === 'quarter') {
            for (let i = 1; i <= 4; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `Q${i}`;
                if (currentPeriodValue == i) option.selected = true;
                periodValueSelect.appendChild(option);
            }
        } else if (periodType === 'month') {
            const months = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];
            months.forEach((month, index) => {
                const option = document.createElement('option');
                option.value = index + 1;
                option.textContent = month;
                if (currentPeriodValue == (index + 1)) option.selected = true;
                periodValueSelect.appendChild(option);
            });
        } else if (periodType === 'half_year') {
            for (let i = 1; i <= 2; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `H${i}`;
                if (currentPeriodValue == i) option.selected = true;
                periodValueSelect.appendChild(option);
            }
        }
    }
    
    periodTypeSelect.addEventListener('change', updatePeriodValues);
    
    // Initialize on page load
    if (periodTypeSelect.value) {
        updatePeriodValues();
    }
});
</script>
@endpush

@push('css')
<style>
    .required::after {
        content: " *";
        color: red;
    }
    
    .form-actions {
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }
    
    .form-actions .btn {
        margin-right: 0.5rem;
    }
</style>
@endpush
