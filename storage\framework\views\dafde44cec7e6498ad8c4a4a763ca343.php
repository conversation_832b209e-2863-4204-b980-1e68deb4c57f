<?php $__env->startSection('page_title'); ?>
    <?php echo e(__('sales::app.targets.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1><?php echo e(__('sales::app.targets.title')); ?></h1>
                <p><?php echo e(__('sales::app.targets.description')); ?></p>
            </div>
            
            <div class="page-action">
                <a href="<?php echo e(route('admin.sales.targets.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> <?php echo e(__('sales::app.targets.create')); ?>

                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="page-content">
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('admin.sales.targets.index')); ?>">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="financial_year">Financial Year</label>
                                    <select name="financial_year" id="financial_year" class="form-control">
                                        <option value="">All Years</option>
                                        <?php if(isset($financialYears)): ?>
                                            <?php $__currentLoopData = $financialYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($year); ?>" <?php echo e(($filters['financial_year'] ?? '') == $year ? 'selected' : ''); ?>>
                                                    <?php echo e($year); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="period_type">Period Type</label>
                                    <select name="period_type" id="period_type" class="form-control">
                                        <option value="">All Periods</option>
                                        <option value="quarter" <?php echo e(($filters['period_type'] ?? '') == 'quarter' ? 'selected' : ''); ?>>Quarter</option>
                                        <option value="month" <?php echo e(($filters['period_type'] ?? '') == 'month' ? 'selected' : ''); ?>>Month</option>
                                        <option value="half_year" <?php echo e(($filters['period_type'] ?? '') == 'half_year' ? 'selected' : ''); ?>>Half Year</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="user_id">Sales Representative</label>
                                    <select name="user_id" id="user_id" class="form-control">
                                        <option value="">All Users</option>
                                        <?php if(isset($users)): ?>
                                            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($user->id); ?>" <?php echo e(($filters['user_id'] ?? '') == $user->id ? 'selected' : ''); ?>>
                                                    <?php echo e($user->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter"></i> Apply Filters
                                        </button>
                                        <a href="<?php echo e(route('admin.sales.targets.index')); ?>" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Targets Table -->
            <?php if(isset($targets) && count($targets) > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Sales Targets</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sales Rep</th>
                                        <th>Period</th>
                                        <th>Target Amount</th>
                                        <th>Current Achievement</th>
                                        <th>Progress</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $targets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $target): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($target->user->name ?? 'N/A'); ?></td>
                                            <td>
                                                <?php echo e(ucfirst(str_replace('_', ' ', $target->period_type))); ?> 
                                                <?php echo e($target->period_value); ?>, <?php echo e($target->financial_year); ?>

                                            </td>
                                            <td><?php echo e(number_format($target->target_amount, 2)); ?></td>
                                            <td><?php echo e(number_format($target->actual_amount ?? 0, 2)); ?></td>
                                            <td>
                                                <?php
                                                    $percentage = $target->target_amount > 0 ? (($target->actual_amount ?? 0) / $target->target_amount) * 100 : 0;
                                                ?>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-<?php echo e($percentage >= 100 ? 'success' : ($percentage >= 75 ? 'warning' : 'danger')); ?>" 
                                                         style="width: <?php echo e(min($percentage, 100)); ?>%">
                                                        <?php echo e(number_format($percentage, 1)); ?>%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if($percentage >= 100): ?>
                                                    <span class="badge badge-success">Target Met</span>
                                                <?php elseif($percentage >= 75): ?>
                                                    <span class="badge badge-warning">On Track</span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">Below Target</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.sales.targets.edit', $target->id)); ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="<?php echo e(route('admin.sales.targets.destroy', $target->id)); ?>" 
                                                          style="display: inline;" 
                                                          onsubmit="return confirm('Are you sure you want to delete this target?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php if(method_exists($targets, 'links')): ?>
                            <div class="mt-3">
                                <?php echo e($targets->links()); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-bullseye fa-3x text-muted mb-3"></i>
                        <h5>No Targets Found</h5>
                        <p class="text-muted">No sales targets have been set up yet.</p>
                        <a href="<?php echo e(route('admin.sales.targets.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create First Target
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css'); ?>
<style>
    .progress {
        background-color: #e9ecef;
    }
    
    .btn-group .btn {
        margin-right: 2px;
    }
    
    .btn-group .btn:last-child {
        margin-right: 0;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin::layouts.content', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Sales\src/resources/views/targets/index.blade.php ENDPATH**/ ?>