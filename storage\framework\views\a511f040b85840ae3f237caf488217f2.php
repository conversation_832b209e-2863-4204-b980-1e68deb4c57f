<?php $__env->startSection('page_title'); ?>
    <?php echo e(__('sales::app.reports.leaderboard.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1><?php echo e(__('sales::app.reports.leaderboard.title')); ?></h1>
                <p><?php echo e(__('sales::app.reports.leaderboard.description')); ?></p>
            </div>
            
            <div class="page-action">
                <a href="<?php echo e(route('admin.sales.reports.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Reports
                </a>
            </div>
        </div>

        <div class="page-content">
            <?php if(isset($topPerformers) && count($topPerformers) > 0): ?>
                <!-- Top 3 Performers -->
                <div class="row mb-4">
                    <?php $__currentLoopData = $topPerformers->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $performer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-4">
                            <div class="card text-center <?php echo e($index == 0 ? 'border-warning' : ($index == 1 ? 'border-info' : 'border-secondary')); ?>">
                                <div class="card-body">
                                    <div class="position-relative mb-3">
                                        <?php if($index == 0): ?>
                                            <i class="fas fa-crown fa-3x text-warning"></i>
                                        <?php elseif($index == 1): ?>
                                            <i class="fas fa-medal fa-3x text-info"></i>
                                        <?php else: ?>
                                            <i class="fas fa-award fa-3x text-secondary"></i>
                                        <?php endif; ?>
                                        <div class="position-absolute top-0 start-0">
                                            <span class="badge badge-<?php echo e($index == 0 ? 'warning' : ($index == 1 ? 'info' : 'secondary')); ?> rounded-pill">
                                                #<?php echo e($index + 1); ?>

                                            </span>
                                        </div>
                                    </div>
                                    <h5 class="card-title"><?php echo e($performer->user_name ?? 'N/A'); ?></h5>
                                    <h3 class="text-<?php echo e($index == 0 ? 'warning' : ($index == 1 ? 'info' : 'secondary')); ?>">
                                        <?php echo e(number_format($performer->achievement_percentage, 1)); ?>%
                                    </h3>
                                    <p class="text-muted mb-2">Achievement Rate</p>
                                    <small class="text-muted">
                                        Target: <?php echo e(number_format($performer->target_amount, 0)); ?><br>
                                        Actual: <?php echo e(number_format($performer->actual_amount, 0)); ?>

                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Full Leaderboard -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy"></i> Complete Leaderboard
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="60">Rank</th>
                                        <th>Sales Representative</th>
                                        <th>Target Amount</th>
                                        <th>Actual Amount</th>
                                        <th>Achievement %</th>
                                        <th>Performance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $topPerformers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $performer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="<?php echo e($index < 3 ? 'table-' . ($index == 0 ? 'warning' : ($index == 1 ? 'info' : 'secondary')) : ''); ?>">
                                            <td>
                                                <span class="badge badge-<?php echo e($index == 0 ? 'warning' : ($index == 1 ? 'info' : ($index == 2 ? 'secondary' : 'light'))); ?> rounded-pill">
                                                    #<?php echo e($index + 1); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if($index < 3): ?>
                                                        <i class="fas fa-<?php echo e($index == 0 ? 'crown' : ($index == 1 ? 'medal' : 'award')); ?> me-2 text-<?php echo e($index == 0 ? 'warning' : ($index == 1 ? 'info' : 'secondary')); ?>"></i>
                                                    <?php endif; ?>
                                                    <strong><?php echo e($performer->user_name ?? 'N/A'); ?></strong>
                                                </div>
                                            </td>
                                            <td><?php echo e(number_format($performer->target_amount, 2)); ?></td>
                                            <td><?php echo e(number_format($performer->actual_amount, 2)); ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 100px; height: 20px;">
                                                        <div class="progress-bar bg-<?php echo e($performer->achievement_percentage >= 100 ? 'success' : ($performer->achievement_percentage >= 75 ? 'warning' : 'danger')); ?>" 
                                                             style="width: <?php echo e(min($performer->achievement_percentage, 100)); ?>%">
                                                        </div>
                                                    </div>
                                                    <span class="badge badge-<?php echo e($performer->achievement_percentage >= 100 ? 'success' : ($performer->achievement_percentage >= 75 ? 'warning' : 'danger')); ?>">
                                                        <?php echo e(number_format($performer->achievement_percentage, 1)); ?>%
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if($performer->achievement_percentage >= 100): ?>
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-check"></i> Exceeded
                                                    </span>
                                                <?php elseif($performer->achievement_percentage >= 90): ?>
                                                    <span class="badge badge-warning">
                                                        <i class="fas fa-star"></i> Excellent
                                                    </span>
                                                <?php elseif($performer->achievement_percentage >= 75): ?>
                                                    <span class="badge badge-info">
                                                        <i class="fas fa-thumbs-up"></i> Good
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">
                                                        <i class="fas fa-arrow-up"></i> Needs Improvement
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                        <h5>No Performance Data</h5>
                        <p class="text-muted">No sales performance data available to display leaderboard.</p>
                        <a href="<?php echo e(route('admin.sales.targets.index')); ?>" class="btn btn-primary">
                            Set Up Targets
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css'); ?>
<style>
    .table-warning {
        background-color: rgba(255, 193, 7, 0.1);
    }
    
    .table-info {
        background-color: rgba(23, 162, 184, 0.1);
    }
    
    .table-secondary {
        background-color: rgba(108, 117, 125, 0.1);
    }
    
    .progress {
        background-color: #e9ecef;
    }
    
    .card.border-warning {
        border-width: 2px;
    }
    
    .card.border-info {
        border-width: 2px;
    }
    
    .card.border-secondary {
        border-width: 2px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin::layouts.content', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Sales\src/resources/views/reports/leaderboard.blade.php ENDPATH**/ ?>