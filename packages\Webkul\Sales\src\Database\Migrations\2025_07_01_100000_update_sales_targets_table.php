<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('crm_sales_targets', function (Blueprint $table) {
            // Add the new column
            $table->tinyInteger('half_year')->nullable()->after('month');

            // Modify the enum
            DB::statement("ALTER TABLE crm_sales_targets MODIFY period_type ENUM('annual', 'half_yearly', 'quarterly', 'monthly', 'custom') DEFAULT 'annual'");

            // Drop the old unique constraint
            $table->dropUnique('unique_user_period_target');

            // Add the new unique constraint
            $table->unique(['user_id', 'financial_year', 'period_type', 'half_year', 'quarter', 'month', 'target_type'], 'unique_user_period_target_new');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('crm_sales_targets', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('unique_user_period_target_new');

            // Add the old unique constraint back
            $table->unique(['user_id', 'financial_year', 'period_type', 'quarter', 'month', 'target_type'], 'unique_user_period_target');

            // Revert the enum (this might not be perfect, but it's the best we can do)
            DB::statement("ALTER TABLE crm_sales_targets MODIFY period_type ENUM('annual', 'quarterly', 'monthly') DEFAULT 'annual'");

            // Drop the column
            $table->dropColumn('half_year');
        });
    }
};
