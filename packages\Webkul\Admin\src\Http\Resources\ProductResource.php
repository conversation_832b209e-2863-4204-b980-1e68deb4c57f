<?php

namespace Webkul\Admin\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $data = [
            'id'              => $this->id,
            'name'            => $this->name,
            'description'     => $this->description,
            'sku'             => $this->sku,
            'price'           => $this->price,
            'created_at'      => $this->created_at,
            'updated_at'      => $this->updated_at,
        ];

        // Add custom attributes
        $customAttributes = [];
        if ($this->attribute_values) {
            foreach ($this->attribute_values as $attribute) {
                $customAttributes[$attribute->attribute->code] = $attribute->value;
            }
        }

        $data['custom_attributes'] = $customAttributes;

        return $data;
    }
}
