<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Webkul\Lead\Models\Lead;
use Webkul\User\Models\User;
use Webkul\Lead\Models\Pipeline;
use Webkul\Lead\Models\Source;
use Webkul\Lead\Models\Type;
use Faker\Factory as Faker;

class LeadDummySeeder extends Seeder
{
    public function run()
    {
        $faker = Faker::create();
        
        // Get required data
        $users = User::pluck('id')->toArray();
        $pipeline = Pipeline::where('is_default', 1)->first();
        $stages = $pipeline->stages->pluck('id')->toArray();
        $sources = Source::pluck('id')->toArray();
        $types = Type::pluck('id')->toArray();
        
        // Create 15 sample persons first
        $personIds = [];
        for ($i = 0; $i < 15; $i++) {
            $personId = DB::table('persons')->insertGetId([
                'name' => $faker->name,
                'emails' => json_encode([['value' => $faker->email]]),
                'contact_numbers' => json_encode([['value' => $faker->phoneNumber]]),
                'created_at' => now(),
                'updated_at' => now()
            ]);
            $personIds[] = $personId;
        }
        
        // Create 15 sample leads
        for ($i = 0; $i < 15; $i++) {
            $leadValue = $faker->randomFloat(2, 1000, 50000);
            
            DB::table('leads')->insert([
                'title' => $faker->catchPhrase(),
                'description' => $faker->paragraph(),
                'lead_value' => $leadValue,
                'status' => 1,
                'user_id' => $faker->randomElement($users),
                'person_id' => $personIds[$i],
                'lead_source_id' => $faker->randomElement($sources),
                'lead_type_id' => $faker->randomElement($types),
                'lead_pipeline_id' => $pipeline->id,
                'lead_pipeline_stage_id' => $faker->randomElement($stages),
                'created_at' => $faker->dateTimeBetween('-3 months', 'now'),
                'updated_at' => now()
            ]);
        }
    }
}