<?php

namespace App\View\Components;

use Closure;
use Illuminate\View\Component;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class Report extends Component
{
    /**
     * The leads data collection or paginator.
     *
     * @var Collection|LengthAwarePaginator
     */
    public $leads;

    /**
     * Create a new component instance.
     *
     * @param Collection|LengthAwarePaginator $leads
     * @return void
     */
    public function __construct($leads)
    {
        $this->leads = $leads;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.report');
    }
}
