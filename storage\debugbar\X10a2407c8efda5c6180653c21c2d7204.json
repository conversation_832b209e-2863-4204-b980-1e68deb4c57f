{"__meta": {"id": "X10a2407c8efda5c6180653c21c2d7204", "datetime": "2025-04-15 23:19:09", "utime": **********.746121, "method": "GET", "uri": "/admin/leads/bulk-upload", "ip": "127.0.0.1"}, "php": {"version": "8.2.25", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:19:09] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$app is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.595033, "xdebug_link": null, "collector": "log"}, {"message": "[23:19:09] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$request is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 22", "message_html": null, "is_string": false, "label": "warning", "time": **********.595193, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.221784, "end": **********.746141, "duration": 0.5243568420410156, "duration_str": "524ms", "measures": [{"label": "Booting", "start": **********.221784, "relative_start": 0, "end": **********.561384, "relative_end": **********.561384, "duration": 0.3395998477935791, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.561396, "relative_start": 0.3396117687225342, "end": **********.746142, "relative_end": 9.5367431640625e-07, "duration": 0.18474602699279785, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 28352120, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 19, "templates": [{"name": "admin::leads.bulk-upload.index", "param_count": null, "params": [], "start": **********.70597, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/leads/bulk-upload/index.blade.phpadmin::leads.bulk-upload.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fleads%2Fbulk-upload%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.form.control-group.label", "param_count": null, "params": [], "start": **********.711362, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/label.blade.phpadmin::components.form.control-group.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "admin::components.form.control-group.control", "param_count": null, "params": [], "start": **********.712003, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/control.blade.phpadmin::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}}, {"name": "admin::components.form.control-group.error", "param_count": null, "params": [], "start": **********.713268, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/error.blade.phpadmin::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}}, {"name": "admin::components.form.control-group.index", "param_count": null, "params": [], "start": **********.713824, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/index.blade.phpadmin::components.form.control-group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.form.index", "param_count": null, "params": [], "start": **********.714304, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/form/index.blade.phpadmin::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.layouts.index", "param_count": null, "params": [], "start": **********.716625, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/index.blade.phpadmin::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.flash-group.index", "param_count": null, "params": [], "start": **********.725941, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/flash-group/index.blade.phpadmin::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.flash-group.item", "param_count": null, "params": [], "start": **********.726663, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/flash-group/item.blade.phpadmin::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "admin::components.modal.confirm", "param_count": null, "params": [], "start": **********.727867, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/modal/confirm.blade.phpadmin::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}}, {"name": "admin::components.layouts.header.index", "param_count": null, "params": [], "start": **********.728668, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/header/index.blade.phpadmin::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.dropdown.index", "param_count": null, "params": [], "start": **********.730823, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/index.blade.phpadmin::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.form.index", "param_count": null, "params": [], "start": **********.731523, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/form/index.blade.phpadmin::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.dropdown.index", "param_count": null, "params": [], "start": **********.731924, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/index.blade.phpadmin::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.header.mega-search.products", "param_count": null, "params": [], "start": **********.732892, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/products.blade.phpadmin::components.shimmer.header.mega-search.products", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fproducts.blade.php&line=1", "ajax": false, "filename": "products.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.header.mega-search.leads", "param_count": null, "params": [], "start": **********.733649, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/leads.blade.phpadmin::components.shimmer.header.mega-search.leads", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fleads.blade.php&line=1", "ajax": false, "filename": "leads.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.header.mega-search.persons", "param_count": null, "params": [], "start": **********.734291, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/persons.blade.phpadmin::components.shimmer.header.mega-search.persons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fpersons.blade.php&line=1", "ajax": false, "filename": "persons.blade.php", "line": "?"}}, {"name": "admin::components.shimmer.header.mega-search.quotes", "param_count": null, "params": [], "start": **********.735276, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/quotes.blade.phpadmin::components.shimmer.header.mega-search.quotes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fquotes.blade.php&line=1", "ajax": false, "filename": "quotes.blade.php", "line": "?"}}, {"name": "admin::components.layouts.sidebar.index", "param_count": null, "params": [], "start": **********.736319, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/sidebar/index.blade.phpadmin::components.layouts.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/leads/bulk-upload", "middleware": "web, admin_locale, user", "controller": "Webkul\\Admin\\Http\\Controllers\\Lead\\BulkUploadController@index", "namespace": null, "prefix": "admin/leads/bulk-upload", "where": [], "as": "admin.leads.bulk_upload.index", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FLead%2FBulkUploadController.php&line=40\" onclick=\"\">packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php:40-49</a>"}, "queries": {"nb_statements": 26, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03423, "accumulated_duration_str": "34.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 557}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}], "start": **********.605767, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:1152", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=1152", "ajax": false, "filename": "BaseRepository.php", "line": "1152"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `core_config` where `code` = 'general.general.locale_settings.locale'", "type": "query", "params": [], "bindings": ["general.general.locale_settings.locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Core.php", "line": 241}], "start": **********.609245, "duration": 0.021929999999999998, "duration_str": "21.93ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 64.067}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.644532, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 64.067, "width_percent": 1.694}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, {"index": 22, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "admin_locale", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.648865, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "user:56", "source": {"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=56", "ajax": false, "filename": "Bouncer.php", "line": "56"}, "connection": "laravel-crm", "explain": null, "start_percent": 65.761, "width_percent": 1.081}, {"sql": "select * from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.650939, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "laravel-crm", "explain": null, "start_percent": 66.842, "width_percent": 1.052}, {"sql": "select * from `persons`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.653331, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "laravel-crm", "explain": null, "start_percent": 67.894, "width_percent": 1.49}, {"sql": "select * from `attributes` where `code` = 'organization_id'", "type": "query", "params": [], "bindings": ["organization_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 23}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 109}, {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 51}], "start": **********.656492, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "laravel-crm", "explain": null, "start_percent": 69.384, "width_percent": 1.607}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 1 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.6608899, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 70.99, "width_percent": 1.694}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 2 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.663118, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 72.685, "width_percent": 1.607}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 3 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.6652632, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 74.292, "width_percent": 1.373}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 4 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.6672232, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 75.665, "width_percent": 1.285}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 5 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.669233, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 76.95, "width_percent": 1.607}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 6 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.67149, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 78.557, "width_percent": 1.665}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 7 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.673585, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 80.222, "width_percent": 1.519}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 8 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.676111, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 81.741, "width_percent": 1.607}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 9 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 9], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.678199, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 83.348, "width_percent": 1.344}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 10 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.680087, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 84.692, "width_percent": 1.285}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 11 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 11], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.6819448, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 85.977, "width_percent": 1.256}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 12 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 12], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.683813, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 87.233, "width_percent": 1.373}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 13 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.685724, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 88.606, "width_percent": 1.344}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 14 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.687869, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 89.95, "width_percent": 1.987}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 15 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 31, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 32, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}], "start": **********.6901531, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 91.937, "width_percent": 1.402}, {"sql": "select * from `lead_pipelines`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.694158, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "laravel-crm", "explain": null, "start_percent": 93.339, "width_percent": 1.344}, {"sql": "select * from `lead_sources`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 45}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6965718, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "laravel-crm", "explain": null, "start_percent": 94.683, "width_percent": 1.227}, {"sql": "select * from `lead_types`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.699137, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "laravel-crm", "explain": null, "start_percent": 95.91, "width_percent": 1.198}, {"sql": "select * from `core_config` where `code` = 'general.design.admin_logo.favicon'", "type": "query", "params": [], "bindings": ["general.design.admin_logo.favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Core.php", "line": 241}], "start": **********.719763, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 97.108, "width_percent": 1.548}, {"sql": "select * from `core_config` where `code` = 'general.content.custom_scripts.custom_css'", "type": "query", "params": [], "bindings": ["general.content.custom_scripts.custom_css"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Core.php", "line": 241}], "start": **********.723125, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 98.656, "width_percent": 1.344}]}, "models": {"data": {"Webkul\\Contact\\Models\\Person": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FContact%2Fsrc%2FModels%2FPerson.php&line=1", "ajax": false, "filename": "Person.php", "line": "?"}}, "Webkul\\Lead\\Models\\Source": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FLead%2Fsrc%2FModels%2FSource.php&line=1", "ajax": false, "filename": "Source.php", "line": "?"}}, "Webkul\\User\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Webkul\\Lead\\Models\\Type": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FLead%2Fsrc%2FModels%2FType.php&line=1", "ajax": false, "filename": "Type.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Lead\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FLead%2Fsrc%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 27, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/leads/bulk-upload\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/admin/leads/bulk-upload", "status_code": "<pre class=sf-dump id=sf-dump-1220129155 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1220129155\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1528634831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1528634831\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-642738647 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-642738647\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-939329835 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Brave&quot;;v=&quot;135&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/admin/leads/bulk-upload</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IlA4SkJERUtBSWxYUjdQenpVZ0VIZnc9PSIsInZhbHVlIjoiMDhYUFduUldkSlNrS3ZKeEFIeTJGTmRJem52K2VjbDJKL1VhZVpBTkcxUWQyZ3VBdW53SWVFdk93VU03UTVtVzFYaXV0Qkw3NUk3SDA1YnJSMGUwUFYzZHdwTURwSDJCL2xTdGgrempEZDVBWFpCMExsUUJXNWk0MUpxVE0yYmEiLCJtYWMiOiI1YWZjNzUyZmQ3MTQ0MmJmNDM4NGI5Y2NjNzc5MjM4Y2QyMTA3OTVjODRiM2Q5NzE3MTExOWFkM2IxZDA1YWIxIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6Ilhob2syV2ZyeWxhZE0vSzFRMjZsZ2c9PSIsInZhbHVlIjoiSWFJWC9pZ1JscHZUU1VoSG41cUtPOUg0ZGs2VCtwVWxNY0IvYjNRbE5oamZSWlExYW1yaVpuUU5QOWFqQkNNR1lFam1VMjQvanZ0RkZSZG4vcWcrU25mVlN1Zm5jaFFIcW1ka3IvdG9pS0RkelE4MWpmeG5qaUpHRjIyZlJnMmciLCJtYWMiOiI2Y2U4MzBhNWYwOTU2NzE1YWJjNDRhZjM4N2YwMmZmNzdjYzc0ZWNlOTAwOTZmZGJlYWI3NDM1ZDg5NjhhNzZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-939329835\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-335610727 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lh4xOQcW5AWaSgG8tSFW592msyWyo1WTTiE9zQSW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335610727\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-908065040 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Apr 2025 17:49:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImIvTzRIUmhOTGx2OW5WemFVZElpQkE9PSIsInZhbHVlIjoidzVjYWJJM2krV0pxOThIMUhaSGxqZGI5czl0VEhTZDVNelZZdkdEMHM0VE9OT1UycGs2V0wvUzRNdmx4SStnMG9VUTRVOWlXWVZvTWNFY25hVENyU0Q4eXZUZTg3MnNSL3lQS2JjVkFIQ215SUs4WTY0OVJKSGxRZDNOeGtDbG8iLCJtYWMiOiJlOGVlZDI3M2E0MWRjMGZjMzJhZmMyZjJjYmRhZjQxODkwYTdiM2ZiYWEyMjBlMmZhZjFkNzg2NzNiMTliNTMwIiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 19:49:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">krayin_crm_session=eyJpdiI6Im1SWnE2UTdpSkZ4VlpsaXNya0NacHc9PSIsInZhbHVlIjoieWxVUFpIeVo4ZVNQTVBWZ1JyQW5wTkRSNlYyRElKTzVFVzNpQm9hakVyK1R3NUIxeFl5QkZ4NmpScHEwUGVNQmNmL251RVZRMlBwMjJLNGk3YXViSDVtdkdxQjl4UUFWTTFtc05jRkpZTjBZVGdOYkl4MEpOZk16TWZzc1VBck0iLCJtYWMiOiI1YTBmYzA5ZGQ3NWU0ZDA2OWVlZTgxOWRjZDNjNGI0NWEyNTM4NjRjNWExNWY0MjBlYmMxZDU4ZGFjNjA2NWMzIiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 19:49:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImIvTzRIUmhOTGx2OW5WemFVZElpQkE9PSIsInZhbHVlIjoidzVjYWJJM2krV0pxOThIMUhaSGxqZGI5czl0VEhTZDVNelZZdkdEMHM0VE9OT1UycGs2V0wvUzRNdmx4SStnMG9VUTRVOWlXWVZvTWNFY25hVENyU0Q4eXZUZTg3MnNSL3lQS2JjVkFIQ215SUs4WTY0OVJKSGxRZDNOeGtDbG8iLCJtYWMiOiJlOGVlZDI3M2E0MWRjMGZjMzJhZmMyZjJjYmRhZjQxODkwYTdiM2ZiYWEyMjBlMmZhZjFkNzg2NzNiMTliNTMwIiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 19:49:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">krayin_crm_session=eyJpdiI6Im1SWnE2UTdpSkZ4VlpsaXNya0NacHc9PSIsInZhbHVlIjoieWxVUFpIeVo4ZVNQTVBWZ1JyQW5wTkRSNlYyRElKTzVFVzNpQm9hakVyK1R3NUIxeFl5QkZ4NmpScHEwUGVNQmNmL251RVZRMlBwMjJLNGk3YXViSDVtdkdxQjl4UUFWTTFtc05jRkpZTjBZVGdOYkl4MEpOZk16TWZzc1VBck0iLCJtYWMiOiI1YTBmYzA5ZGQ3NWU0ZDA2OWVlZTgxOWRjZDNjNGI0NWEyNTM4NjRjNWExNWY0MjBlYmMxZDU4ZGFjNjA2NWMzIiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 19:49:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908065040\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1686910035 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/admin/leads/bulk-upload</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686910035\", {\"maxDepth\":0})</script>\n"}}