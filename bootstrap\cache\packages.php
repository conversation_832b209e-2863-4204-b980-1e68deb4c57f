<?php return array (
  'barryvdh/laravel-dompdf' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Pdf' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
  ),
  'diglactic/laravel-breadcrumbs' => 
  array (
    'providers' => 
    array (
      0 => 'Diglactic\\Breadcrumbs\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Breadcrumbs' => 'Diglactic\\Breadcrumbs\\Breadcrumbs',
    ),
  ),
  'konekt/concord' => 
  array (
    'providers' => 
    array (
      0 => 'Konekt\\Concord\\ConcordServiceProvider',
    ),
    'aliases' => 
    array (
      'Concord' => 'Konekt\\Concord\\Facades\\Concord',
      'Helper' => 'Konekt\\Concord\\Facades\\Helper',
    ),
  ),
  'konekt/enum-eloquent' => 
  array (
    'providers' => 
    array (
      0 => 'Konekt\\Enum\\Eloquent\\EnumServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'prettus/l5-repository' => 
  array (
    'providers' => 
    array (
      0 => 'Prettus\\Repository\\Providers\\RepositoryServiceProvider',
    ),
  ),
);