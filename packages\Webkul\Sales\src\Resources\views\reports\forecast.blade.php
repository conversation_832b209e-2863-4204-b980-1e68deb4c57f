@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.reports.forecast.title') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.reports.forecast.title') }}</h1>
                <p>{{ __('sales::app.reports.forecast.description') }}</p>
            </div>
            
            <div class="page-action">
                <a href="{{ route('admin.sales.reports.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Reports
                </a>
            </div>
        </div>

        <div class="page-content">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-chart-area fa-3x text-info mb-3"></i>
                    <h5>Sales Forecast</h5>
                    <p class="text-muted">Sales forecasting feature is coming soon. This will include predictive analytics based on historical performance data.</p>
                    
                    <div class="mt-4">
                        <h6>Planned Features:</h6>
                        <ul class="list-unstyled text-left d-inline-block">
                            <li><i class="fas fa-check text-success"></i> Historical trend analysis</li>
                            <li><i class="fas fa-check text-success"></i> Seasonal pattern recognition</li>
                            <li><i class="fas fa-check text-success"></i> Performance-based projections</li>
                            <li><i class="fas fa-check text-success"></i> Confidence intervals</li>
                            <li><i class="fas fa-check text-success"></i> Multiple forecasting models</li>
                        </ul>
                    </div>
                    
                    <a href="{{ route('admin.sales.reports.target_vs_actual') }}" class="btn btn-primary">
                        View Current Performance
                    </a>
                </div>
            </div>
        </div>
    </div>
@stop
