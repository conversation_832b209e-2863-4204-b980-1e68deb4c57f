{"__meta": {"id": "X7ca3a80edf10db11cced291157a2f7d2", "datetime": "2025-04-16 01:27:03", "utime": **********.897706, "method": "POST", "uri": "/admin/leads/bulk-upload", "ip": "127.0.0.1"}, "php": {"version": "8.2.25", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[01:27:03] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$app is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.545378, "xdebug_link": null, "collector": "log"}, {"message": "[01:27:03] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$request is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 22", "message_html": null, "is_string": false, "label": "warning", "time": **********.545625, "xdebug_link": null, "collector": "log"}, {"message": "[01:27:03] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php on line 148", "message_html": null, "is_string": false, "label": "warning", "time": **********.824824, "xdebug_link": null, "collector": "log"}, {"message": "[01:27:03] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php on line 148", "message_html": null, "is_string": false, "label": "warning", "time": **********.86723, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.17407, "end": **********.897734, "duration": 0.7236640453338623, "duration_str": "724ms", "measures": [{"label": "Booting", "start": **********.17407, "relative_start": 0, "end": **********.511779, "relative_end": **********.511779, "duration": 0.3377091884613037, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.511792, "relative_start": 0.3377220630645752, "end": **********.897735, "relative_end": 1.1920928955078125e-06, "duration": 0.3859431743621826, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 38253744, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/leads/bulk-upload", "middleware": "web, admin_locale, user", "controller": "Webkul\\Admin\\Http\\Controllers\\Lead\\BulkUploadController@upload", "namespace": null, "prefix": "admin/leads/bulk-upload", "where": [], "as": "admin.leads.bulk_upload.upload", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FLead%2FBulkUploadController.php&line=60\" onclick=\"\">packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php:60-336</a>"}, "queries": {"nb_statements": 30, "nb_visible_statements": 31, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03593, "accumulated_duration_str": "35.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 557}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}], "start": **********.556749, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:1152", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=1152", "ajax": false, "filename": "BaseRepository.php", "line": "1152"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `core_config` where `code` = 'general.general.locale_settings.locale'", "type": "query", "params": [], "bindings": ["general.general.locale_settings.locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Core.php", "line": 241}], "start": **********.560083, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 7.57}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5767732, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 7.57, "width_percent": 1.781}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, {"index": 22, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "admin_locale", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.582923, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "user:56", "source": {"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=56", "ajax": false, "filename": "Bouncer.php", "line": "56"}, "connection": "laravel-crm", "explain": null, "start_percent": 9.352, "width_percent": 1.976}, {"sql": "select * from `persons` where `name` = '<PERSON><PERSON> shaw'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON> shaw"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 23}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.759259, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "laravel-crm", "explain": null, "start_percent": 11.328, "width_percent": 2.06}, {"sql": "select * from `persons` where `name` like '%<PERSON><PERSON> shaw%'", "type": "query", "params": [], "bindings": ["%Raju shaw%"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 114}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7648811, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 13.387, "width_percent": 2.254}, {"sql": "select * from `persons` where `name` = '<PERSON><PERSON> shaw'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON> shaw"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 23}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 131}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.768395, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "laravel-crm", "explain": null, "start_percent": 15.642, "width_percent": 1.419}, {"sql": "insert into `persons` (`name`, `emails`, `contact_numbers`, `updated_at`, `created_at`) values ('<PERSON><PERSON> shaw', '\\\"[{\\\\\\\"value\\\\\\\":\\\\\\\"Raj<PERSON> <EMAIL>\\\\\\\",\\\\\\\"label\\\\\\\":\\\\\\\"work\\\\\\\"}]\\\"', '\\\"[{\\\\\\\"value\\\\\\\":\\\\\\\"1234567890\\\\\\\",\\\\\\\"label\\\\\\\":\\\\\\\"work\\\\\\\"}]\\\"', '2025-04-16 01:27:03', '2025-04-16 01:27:03')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON> shaw", "\"[{\\\"value\\\":\\\"<PERSON><PERSON> <EMAIL>\\\",\\\"label\\\":\\\"work\\\"}]\"", "\"[{\\\"value\\\":\\\"1234567890\\\",\\\"label\\\":\\\"work\\\"}]\"", "2025-04-16 01:27:03", "2025-04-16 01:27:03"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 61}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 138}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.775743, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 17.061, "width_percent": 9.296}, {"sql": "select * from `attributes` where `code` = 'entity'", "type": "query", "params": [], "bindings": ["entity"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 23}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 109}, {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 51}], "start": **********.7815669, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "laravel-crm", "explain": null, "start_percent": 26.357, "width_percent": 2.004}, {"sql": "insert into `activities` (`type`, `title`, `is_done`, `user_id`, `updated_at`, `created_at`) values ('system', 'Created', 1, 1, '2025-04-16 01:27:03', '2025-04-16 01:27:03')", "type": "query", "params": [], "bindings": ["system", "Created", 1, 1, "2025-04-16 01:27:03", "2025-04-16 01:27:03"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Activity/src/Repositories/ActivityRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Repositories\\ActivityRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 25, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 61}], "start": **********.7875679, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 28.361, "width_percent": 6.012}, {"sql": "insert into `person_activities` (`activity_id`, `person_id`) values (26, 16)", "type": "query", "params": [], "bindings": [26, 16], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 19, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 61}, {"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 138}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7994099, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:29", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=29", "ajax": false, "filename": "LogsActivity.php", "line": "29"}, "connection": "laravel-crm", "explain": null, "start_percent": 34.372, "width_percent": 7.431}, {"sql": "select * from `attributes` where (`entity_type` = 'persons')", "type": "query", "params": [], "bindings": ["persons"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 48}, {"index": 16, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 138}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.804352, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "AttributeValueRepository.php:48", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FRepositories%2FAttributeValueRepository.php&line=48", "ajax": false, "filename": "AttributeValueRepository.php", "line": "48"}, "connection": "laravel-crm", "explain": null, "start_percent": 41.804, "width_percent": 1.586}, {"sql": "select * from `attribute_values` where `entity_type` = 'persons' and `entity_id` = 16 and `attribute_id` = 112", "type": "query", "params": [], "bindings": ["persons", 16, 112], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 87}, {"index": 19, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}], "start": **********.8082478, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 43.39, "width_percent": 1.169}, {"sql": "insert into `attribute_values` (`entity_type`, `entity_id`, `attribute_id`, `text_value`) values ('persons', 16, 112, '<PERSON><PERSON> shaw')", "type": "query", "params": [], "bindings": ["persons", 16, 112, "<PERSON><PERSON> shaw"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 17, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 138}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.810963, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 44.559, "width_percent": 4.509}, {"sql": "select * from `persons` where `persons`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 30, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 31, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}, {"index": 32, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 138}], "start": **********.815943, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:17", "source": {"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=17", "ajax": false, "filename": "LogsActivity.php", "line": "17"}, "connection": "laravel-crm", "explain": null, "start_percent": 49.068, "width_percent": 1.447}, {"sql": "select * from `attributes` where `code` = 'organization_id'", "type": "query", "params": [], "bindings": ["organization_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 23}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 109}, {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 51}], "start": **********.81877, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "laravel-crm", "explain": null, "start_percent": 50.515, "width_percent": 1.392}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 16 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 37, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, {"index": 44, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}], "start": **********.821842, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 51.906, "width_percent": 2.06}, {"sql": "select * from `attributes` where `attributes`.`id` = 112 limit 1", "type": "query", "params": [], "bindings": [112], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 72}, {"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 29, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 30, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 31, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}], "start": **********.825305, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:72", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=72", "ajax": false, "filename": "LogsActivity.php", "line": "72"}, "connection": "laravel-crm", "explain": null, "start_percent": 53.966, "width_percent": 1.252}, {"sql": "insert into `activities` (`type`, `title`, `is_done`, `additional`, `user_id`, `updated_at`, `created_at`) values ('system', 'Updated Name', 1, '{\\\"attribute\\\":\\\"Name\\\",\\\"new\\\":{\\\"value\\\":\\\"Raju shaw\\\",\\\"label\\\":\\\"Raju shaw\\\"},\\\"old\\\":{\\\"value\\\":null,\\\"label\\\":null}}', 1, '2025-04-16 01:27:03', '2025-04-16 01:27:03')", "type": "query", "params": [], "bindings": ["system", "Updated Name", 1, "{\"attribute\":\"Name\",\"new\":{\"value\":\"Raj<PERSON> shaw\",\"label\":\"Raj<PERSON> shaw\"},\"old\":{\"value\":null,\"label\":null}}", 1, "2025-04-16 01:27:03", "2025-04-16 01:27:03"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Activity/src/Repositories/ActivityRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Repositories\\ActivityRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 74}, {"index": 18, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}], "start": **********.827766, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 55.218, "width_percent": 4.036}, {"sql": "insert into `person_activities` (`activity_id`, `person_id`) values (27, 16)", "type": "query", "params": [], "bindings": [27, 16], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 93}, {"index": 12, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 20, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 21, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}], "start": **********.83113, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:93", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=93", "ajax": false, "filename": "LogsActivity.php", "line": "93"}, "connection": "laravel-crm", "explain": null, "start_percent": 59.254, "width_percent": 3.924}, {"sql": "insert into `persons` (`name`, `emails`, `contact_numbers`, `updated_at`, `created_at`) values ('<PERSON><PERSON> shaw 67feba0fcc035', '\\\"[{\\\\\\\"value\\\\\\\":\\\\\\\"<PERSON><PERSON> <EMAIL>\\\\\\\",\\\\\\\"label\\\\\\\":\\\\\\\"work\\\\\\\"}]\\\"', '\\\"[{\\\\\\\"value\\\\\\\":\\\\\\\"1234567890\\\\\\\",\\\\\\\"label\\\\\\\":\\\\\\\"work\\\\\\\"}]\\\"', '2025-04-16 01:27:03', '2025-04-16 01:27:03')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON> shaw 67feba0fcc035", "\"[{\\\"value\\\":\\\"<PERSON><PERSON> <EMAIL>\\\",\\\"label\\\":\\\"work\\\"}]\"", "\"[{\\\"value\\\":\\\"1234567890\\\",\\\"label\\\":\\\"work\\\"}]\"", "2025-04-16 01:27:03", "2025-04-16 01:27:03"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 61}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 142}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.835951, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 63.178, "width_percent": 4.704}, {"sql": "insert into `activities` (`type`, `title`, `is_done`, `user_id`, `updated_at`, `created_at`) values ('system', 'Created', 1, 1, '2025-04-16 01:27:03', '2025-04-16 01:27:03')", "type": "query", "params": [], "bindings": ["system", "Created", 1, 1, "2025-04-16 01:27:03", "2025-04-16 01:27:03"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Activity/src/Repositories/ActivityRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Repositories\\ActivityRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 25, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 61}], "start": **********.8424141, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 67.882, "width_percent": 4.676}, {"sql": "insert into `person_activities` (`activity_id`, `person_id`) values (28, 17)", "type": "query", "params": [], "bindings": [28, 17], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 19, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 61}, {"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 142}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8460588, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:29", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=29", "ajax": false, "filename": "LogsActivity.php", "line": "29"}, "connection": "laravel-crm", "explain": null, "start_percent": 72.558, "width_percent": 4.119}, {"sql": "select * from `attributes` where (`entity_type` = 'persons')", "type": "query", "params": [], "bindings": ["persons"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 48}, {"index": 16, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 142}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.850738, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "AttributeValueRepository.php:48", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FRepositories%2FAttributeValueRepository.php&line=48", "ajax": false, "filename": "AttributeValueRepository.php", "line": "48"}, "connection": "laravel-crm", "explain": null, "start_percent": 76.677, "width_percent": 2.004}, {"sql": "select * from `attribute_values` where `entity_type` = 'persons' and `entity_id` = 17 and `attribute_id` = 112", "type": "query", "params": [], "bindings": ["persons", 17, 112], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 87}, {"index": 19, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}], "start": **********.854029, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 78.681, "width_percent": 1.559}, {"sql": "insert into `attribute_values` (`entity_type`, `entity_id`, `attribute_id`, `text_value`) values ('persons', 17, 112, '<PERSON><PERSON> shaw 67feba0fcc035')", "type": "query", "params": [], "bindings": ["persons", 17, 112, "<PERSON><PERSON> shaw 67feba0fcc035"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 17, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 142}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.859261, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 80.239, "width_percent": 4.537}, {"sql": "select * from `persons` where `persons`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 30, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 31, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}, {"index": 32, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Lead/BulkUploadController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Lead\\BulkUploadController.php", "line": 142}], "start": **********.862972, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:17", "source": {"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=17", "ajax": false, "filename": "LogsActivity.php", "line": "17"}, "connection": "laravel-crm", "explain": null, "start_percent": 84.776, "width_percent": 1.058}, {"sql": "select * from `attribute_values` where `attribute_values`.`entity_type` = 'persons' and `attribute_values`.`entity_id` = 17 and `attribute_values`.`entity_id` is not null", "type": "query", "params": [], "bindings": ["persons", 17], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 113}, {"index": 22, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 53}, {"index": 37, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 17}, {"index": 44, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}], "start": **********.8650491, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CustomAttribute.php:59", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Attribute/src/Traits/CustomAttribute.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Traits\\CustomAttribute.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FTraits%2FCustomAttribute.php&line=59", "ajax": false, "filename": "CustomAttribute.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 85.834, "width_percent": 1.781}, {"sql": "select * from `attributes` where `attributes`.`id` = 112 limit 1", "type": "query", "params": [], "bindings": [112], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 72}, {"index": 22, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 29, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 30, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 31, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}], "start": **********.8675618, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:72", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=72", "ajax": false, "filename": "LogsActivity.php", "line": "72"}, "connection": "laravel-crm", "explain": null, "start_percent": 87.615, "width_percent": 1.614}, {"sql": "insert into `activities` (`type`, `title`, `is_done`, `additional`, `user_id`, `updated_at`, `created_at`) values ('system', 'Updated Name', 1, '{\\\"attribute\\\":\\\"Name\\\",\\\"new\\\":{\\\"value\\\":\\\"Raju shaw 67feba0fcc035\\\",\\\"label\\\":\\\"Raju shaw 67feba0fcc035\\\"},\\\"old\\\":{\\\"value\\\":null,\\\"label\\\":null}}', 1, '2025-04-16 01:27:03', '2025-04-16 01:27:03')", "type": "query", "params": [], "bindings": ["system", "Updated Name", 1, "{\"attribute\":\"Name\",\"new\":{\"value\":\"<PERSON><PERSON> shaw 67feba0fcc035\",\"label\":\"<PERSON><PERSON> shaw 67feba0fcc035\"},\"old\":{\"value\":null,\"label\":null}}", 1, "2025-04-16 01:27:03", "2025-04-16 01:27:03"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Activity/src/Repositories/ActivityRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Repositories\\ActivityRepository.php", "line": 39}, {"index": 17, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 74}, {"index": 18, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}], "start": **********.870139, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "laravel-crm", "explain": null, "start_percent": 89.229, "width_percent": 4.425}, {"sql": "insert into `person_activities` (`activity_id`, `person_id`) values (29, 17)", "type": "query", "params": [], "bindings": [29, 17], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 93}, {"index": 12, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 20, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeValueRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeValueRepository.php", "line": 94}, {"index": 21, "namespace": null, "name": "packages/Webkul/Contact/src/Repositories/PersonRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Contact\\src\\Repositories\\PersonRepository.php", "line": 63}], "start": **********.874489, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:93", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Activity/src/Traits/LogsActivity.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Activity\\src\\Traits\\LogsActivity.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FActivity%2Fsrc%2FTraits%2FLogsActivity.php&line=93", "ajax": false, "filename": "LogsActivity.php", "line": "93"}, "connection": "laravel-crm", "explain": null, "start_percent": 93.654, "width_percent": 6.346}]}, "models": {"data": {"Webkul\\Attribute\\Models\\Attribute": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Contact\\Models\\Person": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FContact%2Fsrc%2FModels%2FPerson.php&line=1", "ajax": false, "filename": "Person.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeValue": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeValue.php&line=1", "ajax": false, "filename": "AttributeValue.php", "line": "?"}}, "Webkul\\User\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 21, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/leads/bulk-upload\"\n]", "_flash": "array:2 [\n  \"old\" => array:2 [\n    0 => \"error\"\n    1 => \"import_errors\"\n  ]\n  \"new\" => []\n]", "url": "[]", "login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "error": "1 leads failed to import. Please check the errors below.", "import_errors": "array:1 [\n  0 => \"Row 2: foreach() argument must be of type array|object, string given\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/leads/bulk-upload", "status_code": "<pre class=sf-dump id=sf-dump-1506585103 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1506585103\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1021313330 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1021313330\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-215377103 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215377103\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-636363272 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">10607</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Brave&quot;;v=&quot;135&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryemSXngy9aK1CN6aG</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/admin/leads/bulk-upload</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"729 characters\">dark_mode=0; XSRF-TOKEN=eyJpdiI6IlF2QkZ6THZKS2o1dFhJcG9wTlIvU0E9PSIsInZhbHVlIjoiYTRBaFpWWTQwei8xaGhYeStXeFhwSldzaTE1eHpGNjJ4T0pQbk01NmpJUlFYVkh5UmduK0hrNFFNTXd3aVBjN0podDg3Y3JSS2c2eGVib3UyVVJiVDR5am1jTFZ1R3ovT21iU3JiTWcwM3h2L2JsTm5EbURVNk9za0VZUUxqdFMiLCJtYWMiOiJmZmRhYTk4MmI4ZTkwYTM2MmZiZWU5ZjEyZWVlYTg5NTU0YmRkNzAzYWZkZDE2NmUyM2I3MmM0ZDU3YzJhNjU3IiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6IkYvTW1LT1dLTHBjWTNYdnY0VVZlV1E9PSIsInZhbHVlIjoiY3hTSHhJTzM2YXEwb2xDUmx6WndXKzIwQWxoNVBZbkc1M2t3eStwcnRZRFdsTWtRNkw1a09ZbGlOVkxOZDlid0sxUzRFK1dpTjNBU05yQWVLZWl4cWtORmp6V2h4M1RqSDFoYkxBdzdkYkxpMzE4ZHNJUkhYNUxoZ2hJcjhvVjQiLCJtYWMiOiIzNjUwMjBmZTNjYWVhZmYyZDRkNTUxNmJjMjFhMTBmMjMzZDNlMmNlMTU0MDdjODZkMmJiMTZlYTk4Y2RiYTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636363272\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1925252587 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lh4xOQcW5AWaSgG8tSFW592msyWyo1WTTiE9zQSW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925252587\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-464309069 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Apr 2025 19:57:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/admin/leads/bulk-upload</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlUdThNV2ZTeTJNZkRWSk1peHVwYmc9PSIsInZhbHVlIjoieXNXM3BpK1pNWFA2ZlNiM1pONDVRQ3Rha0VMbUxLWHphNEVES2lDdnU3OTlIbW9TZnN5ZWI4c3VmVnR0cHdoVGFBT0Fmem9tZkNrV2dHZkJXSTZqZGtEakV5MDEybWNORlpoc3dHQVpBdWJ3YXpYKy9GeXM0eklEbnRROEY2U3giLCJtYWMiOiIxMTkyNmE3NDQ1ZTcxYmE2MzI3MGU5ZTUxOTBmNjM0ZjQ3ZWNjNjMwY2Q4ZTFmYWFlNzE1MzIxMjU3NmYwMTRhIiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 21:57:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">krayin_crm_session=eyJpdiI6ImRLSUFtYWFSY3NNNDExMU14UitHRVE9PSIsInZhbHVlIjoiY3FnYi83QnhzT1gralhjdVhFRW16WDRGcTNFNExMZUtLNTArRWYzdlF5T1gvSWVwYlJoNWdJYlUrY1BveWQ2R1UwY0hNV00zSU94YkN0OEtqOUNZQUNJQjh3eWd6WkdTMU1BUnBXcVhCODhvcmlYL2w1RENkU2pBeXVCUFoyZTQiLCJtYWMiOiIzMjUyZjNiZDFkNDVhMzkwY2FkNWUyZjliM2JmZGUwYTk5YWQ2ZDFiOWM4ZjhkZWQ1YTA4NDZhZTM2M2M1YjZkIiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 21:57:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlUdThNV2ZTeTJNZkRWSk1peHVwYmc9PSIsInZhbHVlIjoieXNXM3BpK1pNWFA2ZlNiM1pONDVRQ3Rha0VMbUxLWHphNEVES2lDdnU3OTlIbW9TZnN5ZWI4c3VmVnR0cHdoVGFBT0Fmem9tZkNrV2dHZkJXSTZqZGtEakV5MDEybWNORlpoc3dHQVpBdWJ3YXpYKy9GeXM0eklEbnRROEY2U3giLCJtYWMiOiIxMTkyNmE3NDQ1ZTcxYmE2MzI3MGU5ZTUxOTBmNjM0ZjQ3ZWNjNjMwY2Q4ZTFmYWFlNzE1MzIxMjU3NmYwMTRhIiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 21:57:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">krayin_crm_session=eyJpdiI6ImRLSUFtYWFSY3NNNDExMU14UitHRVE9PSIsInZhbHVlIjoiY3FnYi83QnhzT1gralhjdVhFRW16WDRGcTNFNExMZUtLNTArRWYzdlF5T1gvSWVwYlJoNWdJYlUrY1BveWQ2R1UwY0hNV00zSU94YkN0OEtqOUNZQUNJQjh3eWd6WkdTMU1BUnBXcVhCODhvcmlYL2w1RENkU2pBeXVCUFoyZTQiLCJtYWMiOiIzMjUyZjNiZDFkNDVhMzkwY2FkNWUyZjliM2JmZGUwYTk5YWQ2ZDFiOWM4ZjhkZWQ1YTA4NDZhZTM2M2M1YjZkIiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 21:57:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464309069\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-667889224 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/admin/leads/bulk-upload</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">import_errors</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"56 characters\">1 leads failed to import. Please check the errors below.</span>\"\n  \"<span class=sf-dump-key>import_errors</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">Row 2: foreach() argument must be of type array|object, string given</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667889224\", {\"maxDepth\":0})</script>\n"}}