<?php $__env->startSection('page_title'); ?>
    <?php echo e(__('sales::app.reports.target-vs-actual.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1><?php echo e(__('sales::app.reports.target-vs-actual.title')); ?></h1>
                <p><?php echo e(__('sales::app.reports.target-vs-actual.description')); ?></p>
            </div>
            
            <div class="page-action">
                <a href="<?php echo e(route('admin.sales.reports.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Reports
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="page-content">
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('admin.sales.reports.target_vs_actual')); ?>">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="financial_year">Financial Year</label>
                                    <select name="financial_year" id="financial_year" class="form-control">
                                        <option value="">All Years</option>
                                        <?php for($year = date('Y'); $year >= date('Y') - 5; $year--): ?>
                                            <option value="<?php echo e($year); ?>" <?php echo e(($filters['financial_year'] ?? '') == $year ? 'selected' : ''); ?>>
                                                <?php echo e($year); ?>

                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="period_type">Period Type</label>
                                    <select name="period_type" id="period_type" class="form-control">
                                        <option value="">All Periods</option>
                                        <option value="quarter" <?php echo e(($filters['period_type'] ?? '') == 'quarter' ? 'selected' : ''); ?>>Quarter</option>
                                        <option value="month" <?php echo e(($filters['period_type'] ?? '') == 'month' ? 'selected' : ''); ?>>Month</option>
                                        <option value="half_year" <?php echo e(($filters['period_type'] ?? '') == 'half_year' ? 'selected' : ''); ?>>Half Year</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="user_id">Sales Representative</label>
                                    <select name="user_id" id="user_id" class="form-control">
                                        <option value="">All Users</option>
                                        <?php if(isset($users)): ?>
                                            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($user->id); ?>" <?php echo e(($filters['user_id'] ?? '') == $user->id ? 'selected' : ''); ?>>
                                                    <?php echo e($user->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter"></i> Apply Filters
                                        </button>
                                        <a href="<?php echo e(route('admin.sales.reports.target_vs_actual')); ?>" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Cards -->
            <?php if(isset($reportData) && count($reportData) > 0): ?>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo e(number_format($reportData['total_target_value'] ?? 0, 0)); ?></h4>
                                        <p class="mb-0">Total Target</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-bullseye fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo e(number_format($reportData['total_achieved_value'] ?? 0, 0)); ?></h4>
                                        <p class="mb-0">Total Actual</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-line fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo e(number_format($reportData['avg_achievement_percentage'] ?? 0, 1)); ?>%</h4>
                                        <p class="mb-0">Avg Achievement</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-percentage fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo e($reportData['targets_achieved'] ?? 0); ?></h4>
                                        <p class="mb-0">Targets Met</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-trophy fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Performance Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sales Rep</th>
                                        <th>Period</th>
                                        <th>Target Amount</th>
                                        <th>Actual Amount</th>
                                        <th>Achievement %</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $reportData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($data->user_name ?? 'N/A'); ?></td>
                                            <td><?php echo e($data->period_type); ?> <?php echo e($data->period_value); ?>, <?php echo e($data->financial_year); ?></td>
                                            <td><?php echo e(number_format($data->target_amount, 2)); ?></td>
                                            <td><?php echo e(number_format($data->actual_amount, 2)); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo e($data->achievement_percentage >= 100 ? 'success' : ($data->achievement_percentage >= 75 ? 'warning' : 'danger')); ?>">
                                                    <?php echo e(number_format($data->achievement_percentage, 1)); ?>%
                                                </span>
                                            </td>
                                            <td>
                                                <?php if($data->achievement_percentage >= 100): ?>
                                                    <span class="badge badge-success">Target Met</span>
                                                <?php elseif($data->achievement_percentage >= 75): ?>
                                                    <span class="badge badge-warning">Close</span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">Below Target</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5>No Data Available</h5>
                        <p class="text-muted">No performance data found for the selected criteria.</p>
                        <a href="<?php echo e(route('admin.sales.targets.index')); ?>" class="btn btn-primary">
                            Set Up Targets
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin::layouts.content', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Sales\src/resources/views/reports/target-vs-actual.blade.php ENDPATH**/ ?>