<?php echo view_render_event('admin.contacts.persons.view.attributes.before', ['person' => $person]); ?>


<div class="flex w-full flex-col gap-4 border-b border-gray-200 p-4 dark:border-gray-800">
    <h4 class="font-semibold dark:text-white">
        <?php echo app('translator')->get('admin::app.contacts.persons.view.about-person'); ?>
    </h4>

    <?php echo view_render_event('admin.contacts.persons.view.attributes.form_controls.before', ['person' => $person]); ?>


    <?php if (isset($component)) { $__componentOriginal81b4d293d9113446bb908fc8aef5c8f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.index','data' => ['vSlot' => '{ meta, errors, handleSubmit }','as' => 'div','ref' => 'modalForm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['v-slot' => '{ meta, errors, handleSubmit }','as' => 'div','ref' => 'modalForm']); ?>
        <form @submit="handleSubmit($event, () => {})">
            <?php echo view_render_event('admin.contacts.persons.view.attributes.form_controls.attributes_view.before', ['person' => $person]); ?>


            <?php if (isset($component)) { $__componentOriginalfedffa6c3d6e1212dbb369a6b5fa34f0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfedffa6c3d6e1212dbb369a6b5fa34f0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.attributes.view','data' => ['customAttributes' => app('Webkul\Attribute\Repositories\AttributeRepository')->findWhere([
                    'entity_type' => 'persons',
                    ['code', 'NOTIN', ['name', 'jon_title']]
                ]),'entity' => $person,'url' => route('admin.contacts.persons.update', $person->id),'allowEdit' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::attributes.view'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['custom-attributes' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(app('Webkul\Attribute\Repositories\AttributeRepository')->findWhere([
                    'entity_type' => 'persons',
                    ['code', 'NOTIN', ['name', 'jon_title']]
                ])),'entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($person),'url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.contacts.persons.update', $person->id)),'allow-edit' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfedffa6c3d6e1212dbb369a6b5fa34f0)): ?>
<?php $attributes = $__attributesOriginalfedffa6c3d6e1212dbb369a6b5fa34f0; ?>
<?php unset($__attributesOriginalfedffa6c3d6e1212dbb369a6b5fa34f0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfedffa6c3d6e1212dbb369a6b5fa34f0)): ?>
<?php $component = $__componentOriginalfedffa6c3d6e1212dbb369a6b5fa34f0; ?>
<?php unset($__componentOriginalfedffa6c3d6e1212dbb369a6b5fa34f0); ?>
<?php endif; ?>

            <?php echo view_render_event('admin.contacts.persons.view.attributes.form_controls.attributes_view.after', ['person' => $person]); ?>

        </form>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6)): ?>
<?php $attributes = $__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6; ?>
<?php unset($__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal81b4d293d9113446bb908fc8aef5c8f6)): ?>
<?php $component = $__componentOriginal81b4d293d9113446bb908fc8aef5c8f6; ?>
<?php unset($__componentOriginal81b4d293d9113446bb908fc8aef5c8f6); ?>
<?php endif; ?>

    <?php echo view_render_event('admin.contacts.persons.view.attributes.form_controls.after', ['person' => $person]); ?>

</div>

<?php echo view_render_event('admin.contacts.persons.view.attributes.before', ['person' => $person]); ?>

<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/contacts/persons/view/attributes.blade.php ENDPATH**/ ?>