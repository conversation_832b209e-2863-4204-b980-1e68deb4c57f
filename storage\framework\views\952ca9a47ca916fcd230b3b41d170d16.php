<?php echo view_render_event('admin.leads.index.kanban.before'); ?>


<!-- Kanabn Vue Component -->
<v-leads-kanban>
    <div class="flex flex-col gap-4">
        <!-- Shimmer -->
        <?php if (isset($component)) { $__componentOriginale5b91f699070a773c28036605b8627e3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale5b91f699070a773c28036605b8627e3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.leads.index.kanban','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.leads.index.kanban'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale5b91f699070a773c28036605b8627e3)): ?>
<?php $attributes = $__attributesOriginale5b91f699070a773c28036605b8627e3; ?>
<?php unset($__attributesOriginale5b91f699070a773c28036605b8627e3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale5b91f699070a773c28036605b8627e3)): ?>
<?php $component = $__componentOriginale5b91f699070a773c28036605b8627e3; ?>
<?php unset($__componentOriginale5b91f699070a773c28036605b8627e3); ?>
<?php endif; ?>
    </div>
</v-leads-kanban>

<?php echo view_render_event('admin.leads.index.kanban.after'); ?>


<?php if (! $__env->hasRenderedOnce('7973dff6-12ee-434a-975e-e07bbf04249a')): $__env->markAsRenderedOnce('7973dff6-12ee-434a-975e-e07bbf04249a');
$__env->startPush('scripts'); ?>
    <script type="text/x-template" id="v-leads-kanban-template">
        <template v-if="isLoading">
            <div class="flex flex-col gap-4">
                <?php if (isset($component)) { $__componentOriginale5b91f699070a773c28036605b8627e3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale5b91f699070a773c28036605b8627e3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.leads.index.kanban','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.leads.index.kanban'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale5b91f699070a773c28036605b8627e3)): ?>
<?php $attributes = $__attributesOriginale5b91f699070a773c28036605b8627e3; ?>
<?php unset($__attributesOriginale5b91f699070a773c28036605b8627e3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale5b91f699070a773c28036605b8627e3)): ?>
<?php $component = $__componentOriginale5b91f699070a773c28036605b8627e3; ?>
<?php unset($__componentOriginale5b91f699070a773c28036605b8627e3); ?>
<?php endif; ?>
            </div>
        </template>

        <template v-else>
            <div class="flex flex-col gap-4">
                <?php echo $__env->make('admin::leads.index.kanban.toolbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <?php echo view_render_event('admin.leads.index.kanban.content.before'); ?>


                <div class="flex gap-2.5 overflow-x-auto">
                    <!-- Stage Cards -->
                    <div
                        class="flex min-w-[275px] max-w-[275px] flex-col gap-1 rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900"
                        v-for="(stage, index) in stageLeads"
                    >
                        <?php echo view_render_event('admin.leads.index.kanban.content.stage.header.before'); ?>


                        <!-- Stage Header -->
                        <div class="flex flex-col px-2 py-3">
                            <!-- Stage Title and Action -->
                            <div class="flex items-center justify-between">
                                <span class="text-xs font-medium dark:text-white">
                                    {{ stage.name }} ({{ stage.leads.meta.total }})
                                </span>

                                <?php if(bouncer()->hasPermission('leads.create')): ?>
                                    <a
                                        :href="'<?php echo e(route('admin.leads.create')); ?>' + '?stage_id=' + stage.id"
                                        class="icon-add cursor-pointer rounded p-1 text-lg text-gray-600 transition-all hover:bg-gray-200 hover:text-gray-800 dark:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-white"
                                        target="_blank"
                                    >
                                    </a>
                                <?php endif; ?>
                            </div>

                            <!-- Stage Total Leads and Amount -->
                            <div class="flex items-center justify-between gap-2">
                                <span class="text-xs font-medium dark:text-white">
                                    {{ $admin.formatPrice(stage.lead_value) }}
                                </span>

                                <!-- Progress Bar -->
                                <div class="h-1 w-36 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-800">
                                    <div
                                        class="h-1 bg-green-500"
                                        :style="{ width: (stage.lead_value / totalStagesAmount) * 100 + '%' }"
                                    ></div>
                                </div>
                            </div>
                        </div>

                        <?php echo view_render_event('admin.leads.index.kanban.content.stage.header.after'); ?>

                       
                        <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.before'); ?>


                        <!-- Draggable Stage Lead Cards -->
                        <draggable
                            class="flex h-[calc(100vh-317px)] flex-col gap-2 overflow-y-auto p-2"
                            :class="{ 'justify-center': stage.leads.data.length === 0 }"
                            ghost-class="draggable-ghost"
                            handle=".lead-item"
                            v-bind="{animation: 200}"
                            :list="stage.leads.data"
                            item-key="id"
                            group="leads"
                            @scroll="handleScroll(stage, $event)"
                            @change="updateStage(stage, $event)"
                        >
                            <template #header>
                                <div 
                                    class="flex flex-col items-center justify-center"
                                    v-if="! stage.leads.data.length"
                                >
                                    <img
                                        class="dark:mix-blend-exclusion dark:invert"
                                        src="<?php echo e(vite()->asset('images/empty-placeholders/pipedrive.svg')); ?>"    
                                    >

                                    <div class="flex flex-col items-center gap-4">
                                        <div class="flex flex-col items-center gap-2">
                                            <p class="text-xl font-semibold dark:text-white">
                                                <?php echo app('translator')->get('admin::app.leads.index.kanban.empty-list'); ?>
                                            </p>

                                            <p class="text-gray-400 dark:text-gray-400">
                                                <?php echo app('translator')->get('admin::app.leads.index.kanban.empty-list-description'); ?>
                                            </p>
                                        </div>

                                        <?php if(bouncer()->hasPermission('leads.create')): ?>
                                            <a
                                                :href="'<?php echo e(route('admin.leads.create')); ?>' + '?stage_id=' + stage.id"
                                                class="secondary-button"
                                            >
                                                <?php echo app('translator')->get('admin::app.leads.index.kanban.create-lead-btn'); ?>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </template>

                            <!-- Lead Card -->
                            <template #item="{ element, index }">
                                <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.card.before'); ?>


                                <a
                                    class="lead-item flex cursor-pointer flex-col gap-5 rounded-md border border-gray-100 bg-gray-50 p-2 dark:border-gray-400 dark:bg-gray-400"
                                    :href="'<?php echo e(route('admin.leads.view', 'replaceId')); ?>'.replace('replaceId', element.id)"
                                >
                                    <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.card.header.before'); ?>


                                    <!-- Header -->
                                    <div class="flex items-start justify-between">
                                        <div class="flex items-center gap-1">
                                            <?php if (isset($component)) { $__componentOriginal2d42bddad77c068ade50efea9ce906c7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2d42bddad77c068ade50efea9ce906c7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.avatar.index','data' => [':name' => 'element.person.name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::avatar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':name' => 'element.person.name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2d42bddad77c068ade50efea9ce906c7)): ?>
<?php $attributes = $__attributesOriginal2d42bddad77c068ade50efea9ce906c7; ?>
<?php unset($__attributesOriginal2d42bddad77c068ade50efea9ce906c7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2d42bddad77c068ade50efea9ce906c7)): ?>
<?php $component = $__componentOriginal2d42bddad77c068ade50efea9ce906c7; ?>
<?php unset($__componentOriginal2d42bddad77c068ade50efea9ce906c7); ?>
<?php endif; ?>
                                  
                                            <div class="flex flex-col gap-0.5">
                                                <span class="text-xs font-medium">
                                                    {{ element.person.name }}
                                                </span>

                                                <span class="text-[10px] leading-normal">
                                                    {{ element.person.organization?.name }}
                                                </span>
                                            </div>
                                        </div>

                                        <div
                                            class="group relative"
                                            v-if="element.rotten_days > 0"
                                        >
                                            <span class="icon-rotten cursor-default text-xl text-rose-600"></span>

                                            <div class="absolute -top-1 right-7 hidden w-max flex-col items-center group-hover:flex">
                                                <span class="whitespace-no-wrap relative rounded-md bg-black px-4 py-2 text-xs leading-none text-white shadow-lg">
                                                    {{ "<?php echo app('translator')->get('admin::app.leads.index.kanban.rotten-days', ['days' => 'replaceDays']); ?>".replace('replaceDays', element.rotten_days) }}
                                                </span>

                                                <div class="absolute -right-1 top-2 h-3 w-3 rotate-45 bg-black"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.card.header.after'); ?>


                                    <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.card.title.before'); ?>


                                    <!-- Lead Title -->
                                    <p class="text-xs font-medium">
                                        {{ element.title }}
                                    </p>

                                    <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.card.title.after'); ?>


                                    <div class="flex flex-wrap gap-1">
                                        <div
                                            class="flex items-center gap-1 rounded-xl bg-gray-200 px-2 py-1 text-xs font-medium dark:bg-gray-800 dark:text-white"
                                            v-if="element.user"
                                        >
                                            <span class="icon-settings-user text-sm"></span>
                                            
                                            {{ element.user.name }}
                                        </div>

                                        <div class="rounded-xl bg-gray-200 px-2 py-1 text-xs font-medium dark:bg-gray-800 dark:text-white">
                                            {{ element.formatted_lead_value }}
                                        </div>

                                        <div class="rounded-xl bg-gray-200 px-2 py-1 text-xs font-medium dark:bg-gray-800 dark:text-white">
                                            {{ element.source.name }}
                                        </div>

                                        <div class="rounded-xl bg-gray-200 px-2 py-1 text-xs font-medium dark:bg-gray-800 dark:text-white">
                                            {{ element.type.name }}
                                        </div>

                                        <!-- Tags -->
                                        <template v-for="tag in element.tags">
                                            <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.card.tag.before'); ?>


                                            <div
                                                class="rounded-xl bg-gray-200 px-2 py-1 text-xs font-medium dark:bg-gray-800"
                                                :style="{
                                                    backgroundColor: tag.color,
                                                    color: tagTextColor[tag.color]
                                                }"
                                            >
                                                {{ tag.name }}
                                            </div>

                                            <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.card.tag.after'); ?>

                                        </template>
                                    </div>
                                </a>

                                <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.card.after'); ?>

                            </template>
                        </draggable>

                        <?php echo view_render_event('admin.leads.index.kanban.content.stage.body.after'); ?>

                    </div>
                </div>

                <?php echo view_render_event('admin.leads.index.kanban.content.after'); ?>

            </div>
        </template>
    </script>

    <script type="module">
        app.component('v-leads-kanban', {
            template: '#v-leads-kanban-template',

            data: function () {
                return {
                    available: {
                        columns: <?php echo json_encode($columns, 15, 512) ?>,
                    },

                    applied: {
                        filters: {
                            columns: [],
                        }
                    },

                    stages: <?php echo json_encode($pipeline->stages->toArray(), 15, 512) ?>,

                    stageLeads: {},

                    isLoading: true,

                    tagTextColor: {
                        '#FEE2E2': '#DC2626',
                        '#FFEDD5': '#EA580C',
                        '#FEF3C7': '#D97706',
                        '#FEF9C3': '#CA8A04',
                        '#ECFCCB': '#65A30D',
                        '#DCFCE7': '#16A34A',
                    },
                };
            },

            computed: {
                totalStagesAmount() {
                    let totalAmount = 0;

                    for (let [key, stage] of Object.entries(this.stageLeads)) {
                        totalAmount += parseFloat(stage.lead_value);
                    }

                    return totalAmount;
                }
            },

            mounted: function () {
                this.boot();
            },

            methods: {
                /**
                 * Initialization: This function checks for any previously saved filters in local storage and applies them as needed.
                 *
                 * @returns {void}
                 */
                 boot() {
                    let kanbans = this.getKanbans();

                    if (kanbans?.length) {
                        const currentKanban = kanbans.find(({ src }) => src === this.src);

                        if (currentKanban) {
                            this.applied.filters = currentKanban.applied.filters;

                            this.get()
                                .then(response => {
                                    for (let [stageId, data] of Object.entries(response.data)) {
                                        this.stageLeads[stageId] = data;
                                    }
                                });

                            return;
                        }
                    }

                    this.get()
                        .then(response => {
                            for (let [stageId, data] of Object.entries(response.data)) {
                                this.stageLeads[stageId] = data;
                            }
                        });
                },

                /**
                 * Fetches the leads based on the applied filters.
                 *
                 * @param {object} requestedParams - The requested parameters.
                 * @returns {Promise} The promise object representing the request.
                 */
                get(requestedParams = {}) {
                    let params = {
                        search: '',
                        searchFields: '',
                        pipeline_id: "<?php echo e(request('pipeline_id')); ?>",
                        limit: 10,
                    };

                    this.applied.filters.columns.forEach((column) => {
                        if (column.index === 'all') {
                            if (! column.value.length) {
                                return;
                            }

                            params['search'] += `title:${column.value.join(',')};`;
                            params['searchFields'] += `title:like;`;

                            return;
                        }

                        /**
                         * If the column is a searchable dropdown, then we need to append the column value
                         * with the column label. Otherwise, we can directly append the column value.
                         */
                        params['search'] += column.filterable_type === 'searchable_dropdown'
                            ? `${column.index}:${column.value.map(option => option.value).join(',')};`
                            : `${column.index}:${column.value.join(',')};`;

                        params['searchFields'] += `${column.index}:${column.search_field};`;
                    });

                    return this.$axios
                        .get("<?php echo e(route('admin.leads.get')); ?>", {
                            params: {
                                ...params,

                                ...requestedParams,
                            }
                        })
                        .then(response => {
                            this.isLoading = false;

                            this.updateKanbans();

                            return response;
                        })
                        .catch(error => {
                            console.log(error)
                        });
                },

                /**
                 * Filters the leads based on the applied filters.
                 *
                 * @param {object} filters - The filters to be applied.
                 * @returns {void}
                 */
                filter(filters) {
                    this.applied.filters.columns = [
                        ...(this.applied.filters.columns.filter((column) => column.index === 'all')),
                        ...filters.columns,
                    ];

                    this.get()
                        .then(response => {
                            for (let [stageId, data] of Object.entries(response.data)) {
                                this.stageLeads[stageId] = data;
                            }
                        });
                },

                /**
                 * Searches the leads based on the applied filters.
                 *
                 * @param {object} filters - The filters to be applied.
                 * @returns {void}
                 */
                search(filters) {
                    this.applied.filters.columns = [
                        ...(this.applied.filters.columns.filter((column) => column.index !== 'all')),
                        ...filters.columns,
                    ];

                    this.get()
                        .then(response => {
                            for (let [stageId, data] of Object.entries(response.data)) {
                                this.stageLeads[stageId] = data;
                            }
                        });
                },

                /**
                 * Appends the leads to the stage.
                 *
                 * @param {object} params - The parameters to be appended.
                 * @returns {void}
                 */
                append(params) {
                    this.get(params)
                        .then(response => {
                            for (let [stageId, data] of Object.entries(response.data)) {
                                if (! this.stageLeads[stageId]) {
                                    this.stageLeads[stageId] = data;
                                } else {
                                    this.stageLeads[stageId].leads.data = this.stageLeads[stageId].leads.data.concat(data.leads.data);

                                    this.stageLeads[stageId].leads.meta = data.leads.meta;
                                }
                            }
                        });
                },

                /**
                 * Updates the stage with the latest lead data.
                 *
                 * @param {object} stage - The stage object.
                 * @param {object} event - The event object.
                 * @returns {void}
                 */
                updateStage: function (stage, event) {
                    if (event.removed) {
                        stage.lead_value = parseFloat(stage.lead_value) - parseFloat(event.removed.element.lead_value);

                        this.stageLeads[stage.id].leads.meta.total = this.stageLeads[stage.id].leads.meta.total - 1;

                        return;
                    }

                    stage.lead_value = parseFloat(stage.lead_value) + parseFloat(event.added.element.lead_value);

                    this.stageLeads[stage.id].leads.meta.total = this.stageLeads[stage.id].leads.meta.total + 1;

                    this.$axios
                        .put("<?php echo e(route('admin.leads.stage.update', 'replace')); ?>".replace('replace', event.added.element.id), {
                            'lead_pipeline_stage_id': stage.id
                        })
                        .then(response => {
                            this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                        })
                        .catch(error => {
                            this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });
                        });
                },

                /**
                 * Handles the scroll event on the stage leads.
                 *
                 * @param {object} stage - The stage object.
                 * @param {object} event - The scroll event.
                 * @returns {void}
                 */
                handleScroll(stage, event) {
                    const bottom = event.target.scrollHeight - event.target.scrollTop === event.target.clientHeight

                    if (! bottom) {
                        return;
                    }

                    if (this.stageLeads[stage.id].leads.meta.current_page == this.stageLeads[stage.id].leads.meta.last_page) {
                        return;
                    }

                    this.append({
                        pipeline_stage_id: stage.id,
                        pipeline_id: stage.lead_pipeline_id,
                        page: this.stageLeads[stage.id].leads.meta.current_page + 1,
                        limit: 10,
                    });
                },

                //=======================================================================================
                // Support for previous applied values in kanbans. All code is based on local storage.
                //=======================================================================================

                /**
                 * Updates the kanbans stored in local storage with the latest data.
                 *
                 * @returns {void}
                 */
                 updateKanbans() {
                    let kanbans = this.getKanbans();

                    if (kanbans?.length) {
                        const currentKanban = kanbans.find(({ src }) => src === this.src);

                        if (currentKanban) {
                            kanbans = kanbans.map(kanban => {
                                if (kanban.src === this.src) {
                                    return {
                                        ...kanban,
                                        requestCount: ++kanban.requestCount,
                                        available: this.available,
                                        applied: this.applied,
                                    };
                                }

                                return kanban;
                            });
                        } else {
                            kanbans.push(this.getKanbanInitialProperties());
                        }
                    } else {
                        kanbans = [this.getKanbanInitialProperties()];
                    }

                    this.setKanbans(kanbans);
                },

                /**
                 * Returns the initial properties for a kanban.
                 *
                 * @returns {object} Initial properties for a kanban.
                 */
                getKanbanInitialProperties() {
                    return {
                        src: this.src,
                        requestCount: 0,
                        available: this.available,
                        applied: this.applied,
                    };
                },

                /**
                 * Returns the storage key for kanbans in local storage.
                 *
                 * @returns {string} Storage key for kanbans.
                 */
                getKanbansStorageKey() {
                    return 'kanbans';
                },

                /**
                 * Retrieves the kanbans stored in local storage.
                 *
                 * @returns {Array} Kanbans stored in local storage.
                 */
                getKanbans() {
                    let kanbans = localStorage.getItem(
                        this.getKanbansStorageKey()
                    );

                    return JSON.parse(kanbans) ?? [];
                },

                /**
                 * Sets the kanbans in local storage.
                 *
                 * @param {Array} kanbans - Kanbans to be stored in local storage.
                 * @returns {void}
                 */
                setKanbans(kanbans) {
                    localStorage.setItem(
                        this.getKanbansStorageKey(),
                        JSON.stringify(kanbans)
                    );
                },
            }
        });
    </script>
<?php $__env->stopPush(); endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/leads/index/kanban.blade.php ENDPATH**/ ?>