<!DOCTYPE html>
<html>
<head>
    <title>Sales Component Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Sales Component Test Page</h1>
    
    <h2>Database Tables Status</h2>
    <div class="info">
        <p>✅ CRM Sales tables created successfully with new naming convention:</p>
        <ul>
            <li>crm_sales_targets</li>
            <li>crm_sales_achievements</li>
            <li>crm_sales_teams</li>
            <li>crm_sales_team_members</li>
        </ul>
    </div>

    <h2>Sample Data</h2>
    @php
        try {
            $targets = \Webkul\Sales\Models\SalesTarget::with('user')->take(5)->get();
            $achievements = \Webkul\Sales\Models\SalesAchievement::with('user')->take(5)->get();
            $teams = \Webkul\Sales\Models\SalesTeam::with('teamLead')->get();
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    @endphp

    @if(isset($error))
        <div class="error">Error: {{ $error }}</div>
    @else
        <h3>Sales Targets (Sample)</h3>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>User</th>
                    <th>Financial Year</th>
                    <th>Period Type</th>
                    <th>Target Type</th>
                    <th>Target Value</th>
                    <th>Achieved Value</th>
                    <th>Achievement %</th>
                </tr>
            </thead>
            <tbody>
                @foreach($targets as $target)
                <tr>
                    <td>{{ $target->id }}</td>
                    <td>{{ $target->user->name }}</td>
                    <td>{{ $target->financial_year }}</td>
                    <td>{{ ucfirst($target->period_type) }}</td>
                    <td>{{ ucfirst($target->target_type) }}</td>
                    <td>${{ number_format($target->target_value, 2) }}</td>
                    <td>${{ number_format($target->achieved_value, 2) }}</td>
                    <td>{{ number_format($target->achievement_percentage, 2) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <h3>Sales Achievements (Sample)</h3>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>User</th>
                    <th>Achievement Date</th>
                    <th>Type</th>
                    <th>Value</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                @foreach($achievements as $achievement)
                <tr>
                    <td>{{ $achievement->id }}</td>
                    <td>{{ $achievement->user->name }}</td>
                    <td>{{ $achievement->achievement_date->format('Y-m-d') }}</td>
                    <td>{{ ucfirst($achievement->achievement_type) }}</td>
                    <td>${{ number_format($achievement->achievement_value, 2) }}</td>
                    <td>{{ $achievement->description }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <h3>Sales Teams</h3>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Team Lead</th>
                    <th>Status</th>
                    <th>Regions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($teams as $team)
                <tr>
                    <td>{{ $team->id }}</td>
                    <td>{{ $team->name }}</td>
                    <td>{{ $team->teamLead->name }}</td>
                    <td>{{ ucfirst($team->status) }}</td>
                    <td>{{ implode(', ', $team->regions ?? []) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    @endif

    <h2>Available Routes</h2>
    <div class="info">
        <p>Sales component routes are available at:</p>
        <ul>
            <li><a href="/admin/sales" target="_blank">/admin/sales</a> - Sales Dashboard</li>
            <li><a href="/admin/sales/targets" target="_blank">/admin/sales/targets</a> - Sales Targets Management</li>
            <li><a href="/admin/sales/performance" target="_blank">/admin/sales/performance</a> - Performance Tracking</li>
            <li><a href="/admin/sales/reports" target="_blank">/admin/sales/reports</a> - Sales Reports</li>
        </ul>
    </div>

    <h2>Next Steps</h2>
    <div class="info">
        <p>The Sales component is now ready! You can:</p>
        <ol>
            <li>Access the admin panel and navigate to the Sales section</li>
            <li>Create new sales targets for users</li>
            <li>Track performance against targets</li>
            <li>Generate reports and analytics</li>
            <li>Manage sales teams and assignments</li>
        </ol>
    </div>
</body>
</html>
