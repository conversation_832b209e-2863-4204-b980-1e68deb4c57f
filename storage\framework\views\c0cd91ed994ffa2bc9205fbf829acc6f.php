<?php if (isset($component)) { $__componentOriginal518f66ffb386c691fbbac0c43b8d02ae = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal518f66ffb386c691fbbac0c43b8d02ae = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.controls.inline.email','data' => [':name' => '\''.e($attribute->code).'\'','value' => $value,'rules' => 'required|decimal:4','position' => 'left','label' => $attribute->name,':errors' => 'errors','placeholder' => $attribute->name,'url' => $url,'allowEdit' => $allowEdit]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.controls.inline.email'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':name' => '\''.e($attribute->code).'\'','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($value),'rules' => 'required|decimal:4','position' => 'left','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attribute->name),':errors' => 'errors','placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attribute->name),'url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($url),'allow-edit' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allowEdit)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal518f66ffb386c691fbbac0c43b8d02ae)): ?>
<?php $attributes = $__attributesOriginal518f66ffb386c691fbbac0c43b8d02ae; ?>
<?php unset($__attributesOriginal518f66ffb386c691fbbac0c43b8d02ae); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal518f66ffb386c691fbbac0c43b8d02ae)): ?>
<?php $component = $__componentOriginal518f66ffb386c691fbbac0c43b8d02ae; ?>
<?php unset($__componentOriginal518f66ffb386c691fbbac0c43b8d02ae); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/components/attributes/view/email.blade.php ENDPATH**/ ?>