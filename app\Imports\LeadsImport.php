<?php
namespace App\Imports;

use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use App\Models\Lead;

class LeadsImport implements ToModel, WithHeadingRow
{
    public function model(array $row)
    {
        // Map your columns as needed
        return new Lead([
            'title' => $row['title'] ?? null,
            'lead_value' => $row['lead_value'] ?? null,
            'person_id' => $row['person_id'] ?? null,
            'user_id' => $row['user_id'] ?? null,
            'source_id' => $row['source_id'] ?? null,
            'type_id' => $row['type_id'] ?? null,
            'stage_id' => $row['stage_id'] ?? null,
            'status' => $row['status'] ?? null,
            'description' => $row['description'] ?? null,
            'lost_reason' => $row['lost_reason'] ?? null,
            'expected_close_date' => $row['expected_close_date'] ?? null,
            'closed_at' => $row['closed_at'] ?? null,
        ]);
    }
}
