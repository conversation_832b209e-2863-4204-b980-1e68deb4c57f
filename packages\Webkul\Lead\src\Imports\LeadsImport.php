<?php

namespace Webkul\Lead\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Webkul\Contact\Repositories\PersonRepository;
use Webkul\Lead\Repositories\LeadRepository;
use Webkul\Lead\Repositories\PipelineRepository;
use Webkul\Lead\Repositories\SourceRepository;
use Webkul\Lead\Repositories\StageRepository;
use Webkul\Lead\Repositories\TypeRepository;
use Webkul\User\Repositories\UserRepository;

class LeadsImport implements ToCollection, WithHeadingRow, WithValidation
{
    /**
     * Create a new instance.
     *
     * @return void
     */
    public function __construct(
        protected LeadRepository $leadRepository,
        protected PersonRepository $personRepository,
        protected PipelineRepository $pipelineRepository,
        protected SourceRepository $sourceRepository,
        protected StageRepository $stageRepository,
        protected TypeRepository $typeRepository,
        protected UserRepository $userRepository
    ) {}

    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            $data = [
                'title'               => $row['title'],
                'description'         => $row['description'] ?? null,
                'lead_value'          => $row['lead_value'] ?? null,
                'status'              => $row['status'] ?? 1,
                'lost_reason'         => $row['lost_reason'] ?? null,
                'expected_close_date' => $row['expected_close_date'] ?? null,
                'closed_at'           => $row['closed_at'] ?? null,
            ];

            // Map person_id if name is provided
            if (! empty($row['person_name'])) {
                $person = $this->personRepository->findOneByField('name', $row['person_name']);
                if ($person) {
                    $data['person_id'] = $person->id;
                } else {
                    // If person not found by exact name, try case-insensitive search
                    $persons = $this->personRepository->findWhere([
                        ['name', 'like', '%'.$row['person_name'].'%'],
                    ]);

                    if ($persons->count() > 0) {
                        $data['person_id'] = $persons->first()->id;
                    } else {
                        // Skip this row if person is required but not found
                        continue;
                    }
                }
            } elseif (! empty($row['person_id'])) {
                $data['person_id'] = $row['person_id'];
            } else {
                // Skip this row if person is required but not provided
                continue;
            }

            // Map user_id if name is provided
            if (! empty($row['user_name'])) {
                $user = $this->userRepository->findOneByField('name', $row['user_name']);
                if ($user) {
                    $data['user_id'] = $user->id;
                }
            } elseif (! empty($row['user_id'])) {
                $data['user_id'] = $row['user_id'];
            } else {
                $data['user_id'] = auth()->guard('user')->user()->id;
            }

            // Map source_id if name is provided
            if (! empty($row['source_name'])) {
                $source = $this->sourceRepository->findOneByField('name', $row['source_name']);
                if ($source) {
                    $data['lead_source_id'] = $source->id;
                }
            } elseif (! empty($row['lead_source_id'])) {
                $data['lead_source_id'] = $row['lead_source_id'];
            }

            // Map type_id if name is provided
            if (! empty($row['type_name'])) {
                $type = $this->typeRepository->findOneByField('name', $row['type_name']);
                if ($type) {
                    $data['lead_type_id'] = $type->id;
                }
            } elseif (! empty($row['lead_type_id'])) {
                $data['lead_type_id'] = $row['lead_type_id'];
            }

            // Map pipeline and stage
            if (! empty($row['pipeline_name'])) {
                $pipeline = $this->pipelineRepository->findOneByField('name', $row['pipeline_name']);
                if ($pipeline) {
                    $data['lead_pipeline_id'] = $pipeline->id;

                    // Get the first stage of the pipeline if stage is not specified
                    if (empty($row['stage_name']) && empty($row['lead_pipeline_stage_id'])) {
                        $stage = $pipeline->stages()->orderBy('sort_order', 'ASC')->first();
                        if ($stage) {
                            $data['lead_pipeline_stage_id'] = $stage->id;
                        }
                    }
                }
            } elseif (! empty($row['lead_pipeline_id'])) {
                $data['lead_pipeline_id'] = $row['lead_pipeline_id'];
            }

            // Map stage_id if name is provided
            if (! empty($row['stage_name']) && ! empty($data['lead_pipeline_id'])) {
                $stage = $this->stageRepository->findOneWhere([
                    'name'             => $row['stage_name'],
                    'lead_pipeline_id' => $data['lead_pipeline_id'],
                ]);

                if ($stage) {
                    $data['lead_pipeline_stage_id'] = $stage->id;
                }
            } elseif (! empty($row['lead_pipeline_stage_id'])) {
                $data['lead_pipeline_stage_id'] = $row['lead_pipeline_stage_id'];
            }

            // Create the lead
            $this->leadRepository->create($data);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title'               => 'required|string|max:255',
            'description'         => 'nullable|string',
            'lead_value'          => 'nullable|numeric',
            'status'              => 'nullable|boolean',
            'expected_close_date' => 'nullable|date',
            'closed_at'           => 'nullable|date',
        ];
    }
}
