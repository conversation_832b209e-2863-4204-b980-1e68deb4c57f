<?php

namespace Webkul\Admin\Http\Controllers\Lead;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Contact\Repositories\PersonRepository;
use Webkul\Lead\Repositories\LeadRepository;
use Webkul\Lead\Repositories\PipelineRepository;
use Webkul\Lead\Repositories\SourceRepository;
use Webkul\Lead\Repositories\StageRepository;
use Webkul\Lead\Repositories\TypeRepository;
use Webkul\User\Repositories\UserRepository;
use Webkul\Attribute\Repositories\AttributeRepository;
use Webkul\Attribute\Repositories\AttributeValueRepository;

class BulkUploadController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected LeadRepository $leadRepository,
        protected PersonRepository $personRepository,
        protected PipelineRepository $pipelineRepository,
        protected SourceRepository $sourceRepository,
        protected StageRepository $stageRepository,
        protected TypeRepository $typeRepository,
        protected UserRepository $userRepository,
        protected AttributeRepository $attributeRepository,
        protected AttributeValueRepository $attributeValueRepository
    ) {
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $users = $this->userRepository->all();
        $persons = $this->personRepository->all();
        $pipelines = $this->pipelineRepository->all();
        $sources = $this->sourceRepository->all();
        $types = $this->typeRepository->all();

        return view('admin::leads.bulk-upload.index', compact('users', 'persons', 'pipelines', 'sources', 'types'));
    }

    /**
     * Process the uploaded file and import leads.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function upload(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,xls,xlsx',
        ]);

        if ($validator->fails()) {
            session()->flash('error', $validator->errors()->first());

            return redirect()->back();
        }

        try {
            $file = $request->file('file');
            $rows = Excel::toArray([], $file)[0]; // Get the first sheet

            // Ensure the file has headers and data
            if (empty($rows) || count($rows) < 2) {
                session()->flash('error', trans('admin::app.leads.bulk-upload.invalid-file'));

                return redirect()->back();
            }

            // Extract headers and map them to Krayin CRM fields
            $headers = array_map('strtolower', $rows[0]);
            $leads = array_slice($rows, 1); // Exclude the header row

            // Process each lead
            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($leads as $index => $lead) {
                if (count($lead) !== count($headers)) {
                    $errorCount++;
                    $errors[] = "Row " . ($index + 2) . ": Column count mismatch";
                    continue;
                }

                $leadData = array_combine($headers, $lead);

                try {
                    // Required fields validation
                    if (empty($leadData['title'])) {
                        throw new \Exception("Title is required");
                    }

                    // Map person_id if name is provided
                    if (!empty($leadData['person_name']) && empty($leadData['person_id'])) {
                        $person = $this->personRepository->findOneByField('name', $leadData['person_name']);
                        if ($person) {
                            $leadData['person_id'] = $person->id;
                        } else {
                            // Try case-insensitive search
                            $persons = $this->personRepository->findWhere([
                                ['name', 'like', '%' . $leadData['person_name'] . '%']
                            ]);

                            if ($persons->count() > 0) {
                                $leadData['person_id'] = $persons->first()->id;
                            } else {
                                // Create a new person if not found
                                // Check if email is provided in the lead data
                                $personData = [
                                    'name' => $leadData['person_name'],
                                    'entity_type' => 'persons'
                                ];

                                // Create the person
                                $newPerson = $this->personRepository->create($personData);

                                $leadData['person_id'] = $newPerson->id;
                            }
                        }
                    }

                    // Map source_id if name is provided
                    if (!empty($leadData['source_name']) && empty($leadData['lead_source_id'])) {
                        $source = $this->sourceRepository->findOneByField('name', $leadData['source_name']);
                        if ($source) {
                            $leadData['lead_source_id'] = $source->id;
                        } else {
                            throw new \Exception("Source not found: " . $leadData['source_name']);
                        }
                    }

                    // Map type_id if name is provided
                    if (!empty($leadData['type_name']) && empty($leadData['lead_type_id'])) {
                        $type = $this->typeRepository->findOneByField('name', $leadData['type_name']);
                        if ($type) {
                            $leadData['lead_type_id'] = $type->id;
                        } else {
                            throw new \Exception("Type not found: " . $leadData['type_name']);
                        }
                    }

                    // Map pipeline and stage
                    if (!empty($leadData['pipeline_name']) && empty($leadData['lead_pipeline_id'])) {
                        $pipeline = $this->pipelineRepository->findOneByField('name', $leadData['pipeline_name']);
                        if ($pipeline) {
                            $leadData['lead_pipeline_id'] = $pipeline->id;

                            // Get the first stage of the pipeline if stage is not specified
                            if (empty($leadData['stage_name']) && empty($leadData['lead_pipeline_stage_id'])) {
                                $stage = $pipeline->stages()->orderBy('sort_order', 'ASC')->first();
                                if ($stage) {
                                    $leadData['lead_pipeline_stage_id'] = $stage->id;
                                }
                            }
                        } else {
                            throw new \Exception("Pipeline not found: " . $leadData['pipeline_name']);
                        }
                    }

                    // Map stage_id if name is provided
                    if (!empty($leadData['stage_name']) && empty($leadData['lead_pipeline_stage_id'])) {
                        $pipelineId = $leadData['lead_pipeline_id'] ?? null;

                        if ($pipelineId) {
                            $stage = $this->stageRepository->findOneWhere([
                                'name' => $leadData['stage_name'],
                                'lead_pipeline_id' => $pipelineId
                            ]);

                            if ($stage) {
                                $leadData['lead_pipeline_stage_id'] = $stage->id;
                            } else {
                                throw new \Exception("Stage not found: " . $leadData['stage_name']);
                            }
                        } else {
                            throw new \Exception("Pipeline ID is required to map stage name");
                        }
                    }

                    // Map user_id if name is provided
                    if (!empty($leadData['user_name']) && empty($leadData['user_id'])) {
                        $user = $this->userRepository->findOneByField('name', $leadData['user_name']);
                        if ($user) {
                            $leadData['user_id'] = $user->id;
                        } else {
                            throw new \Exception("User not found: " . $leadData['user_name']);
                        }
                    }

                    // Prepare data for lead creation
                    $data = [
                        'title' => $leadData['title'],
                        'description' => $leadData['description'] ?? null,
                        'lead_value' => $leadData['lead_value'] ?? null,
                        'status' => $leadData['status'] ?? 1,
                        'lost_reason' => $leadData['lost_reason'] ?? null,
                        'expected_close_date' => $leadData['expected_close_date'] ?? null,
                        'closed_at' => $leadData['closed_at'] ?? null,
                        'user_id' => $leadData['user_id'] ?? auth()->guard('user')->user()->id,
                        'person_id' => $leadData['person_id'] ?? null,
                        'lead_source_id' => $leadData['lead_source_id'] ?? null,
                        'lead_type_id' => $leadData['lead_type_id'] ?? null,
                        'lead_pipeline_id' => $leadData['lead_pipeline_id'] ?? null,
                        'lead_pipeline_stage_id' => $leadData['lead_pipeline_stage_id'] ?? null,
                    ];

                    // Get custom attributes
                    $customAttributes = [];
                    $attributes = $this->attributeRepository->findWhere([
                        'entity_type' => 'leads',
                        ['code', 'NOTIN', ['title', 'description']],
                    ]);

                    foreach ($attributes as $attribute) {
                        if (isset($leadData[$attribute->code])) {
                            $customAttributes[$attribute->code] = $leadData[$attribute->code];
                        }
                    }

                    // Merge custom attributes with standard data
                    $data = array_merge($data, $customAttributes);

                    // Create the lead
                    $lead = $this->leadRepository->create($data);

                    // Save custom attribute values
                    if (!empty($customAttributes)) {
                        $this->attributeValueRepository->save(array_merge($customAttributes, [
                            'entity_id' => $lead->id,
                            'entity_type' => 'leads'
                        ]));
                    }

                    $successCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = "Row " . ($index + 2) . ": " . $e->getMessage();
                }
            }

            // Flash appropriate message
            if ($successCount > 0) {
                session()->flash('success', trans('admin::app.leads.bulk-upload.success', ['count' => $successCount]));
            }

            if ($errorCount > 0) {
                session()->flash('error', trans('admin::app.leads.bulk-upload.error', ['count' => $errorCount]));
                session()->flash('import_errors', $errors);
            }

            return redirect()->route('admin.leads.bulk_upload.index');
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());

            return redirect()->back();
        }
    }

    /**
     * Download sample CSV file.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadSample()
    {
        $headers = [
            'title',
            'description',
            'lead_value',
            'status',
            'expected_close_date',
            'person_name',
            'user_name',
            'source_name',
            'type_name',
            'pipeline_name',
            'stage_name',
        ];

        // Add custom attributes to the sample headers
        $customAttributes = $this->attributeRepository->findWhere([
            'entity_type' => 'leads',
            ['code', 'NOTIN', ['title', 'description']],
        ]);

        foreach ($customAttributes as $attribute) {
            if (!in_array($attribute->code, $headers)) {
                $headers[] = $attribute->code;
            }
        }

        $sampleData = [
            $headers,
            array_merge([
                'Sample Lead Title',
                'This is a sample lead description',
                '1000',
                '1',
                date('Y-m-d'),
                'John Doe',
                'Admin',
                'Website',
                'Hot',
                'Sales Pipeline',
                'New',
            ], array_fill(0, count($headers) - 11, '')) // Fill remaining columns with empty values
        ];

        $callback = function() use ($sampleData) {
            $file = fopen('php://output', 'w');

            foreach ($sampleData as $row) {
                fputcsv($file, $row);
            }

            fclose($file);
        };

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="sample_leads_import.csv"',
        ];

        return response()->stream($callback, 200, $headers);
    }
}
