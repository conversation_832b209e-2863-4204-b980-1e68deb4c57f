@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.performance.team.title') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.performance.team.title') }}</h1>
                <p>{{ __('sales::app.performance.team.description') }}</p>
            </div>
            
            <div class="page-action">
                <a href="{{ route('admin.sales.performance.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Performance
                </a>
            </div>
        </div>

        <div class="page-content">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-3x text-info mb-3"></i>
                    <h5>Team Performance</h5>
                    <p class="text-muted">Team performance tracking feature is coming soon. This will include team-based analytics and comparisons.</p>
                    
                    <div class="mt-4">
                        <h6>Planned Features:</h6>
                        <ul class="list-unstyled text-left d-inline-block">
                            <li><i class="fas fa-check text-success"></i> Team vs individual performance</li>
                            <li><i class="fas fa-check text-success"></i> Team target aggregation</li>
                            <li><i class="fas fa-check text-success"></i> Cross-team comparisons</li>
                            <li><i class="fas fa-check text-success"></i> Team collaboration metrics</li>
                            <li><i class="fas fa-check text-success"></i> Manager dashboard</li>
                        </ul>
                    </div>
                    
                    <a href="{{ route('admin.sales.performance.index') }}" class="btn btn-primary">
                        View Individual Performance
                    </a>
                </div>
            </div>
        </div>
    </div>
@stop
