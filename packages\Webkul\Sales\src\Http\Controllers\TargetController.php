<?php

namespace Webkul\Sales\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Sales\Repositories\SalesTargetRepository;
use Webkul\User\Repositories\UserRepository;
use Webkul\Sales\Http\Requests\SalesTargetRequest;

class TargetController extends Controller
{
    public function __construct(
        protected SalesTargetRepository $salesTargetRepository,
        protected UserRepository $userRepository
    ) {
    }

    /**
     * Display a listing of sales targets.
     */
    public function index(Request $request): View
    {
        $filters = $request->only(['financial_year', 'period_type', 'target_type', 'user_id']);
        
        $targets = $this->salesTargetRepository->with(['user', 'achievements'])
            ->when($filters['financial_year'] ?? null, function ($query, $year) {
                return $query->where('financial_year', $year);
            })
            ->when($filters['period_type'] ?? null, function ($query, $type) {
                return $query->where('period_type', $type);
            })
            ->when($filters['target_type'] ?? null, function ($query, $type) {
                return $query->where('target_type', $type);
            })
            ->when($filters['user_id'] ?? null, function ($query, $userId) {
                return $query->where('user_id', $userId);
            })
            ->orderBy('financial_year', 'desc')
            ->orderBy('period_type')
            ->paginate(15);

        $users = $this->userRepository->all(['id', 'name']);
        
        // Get available financial years
        $financialYears = $this->salesTargetRepository
            ->distinct()
            ->pluck('financial_year')
            ->sort()
            ->values();

        return view('sales::targets.index', compact('targets', 'users', 'financialYears', 'filters'));
    }

    /**
     * Show the form for creating a new sales target.
     */
    public function create(): View
    {
        $users = $this->userRepository->all(['id', 'name']);
        
        return view('sales::targets.create', compact('users'));
    }

    /**
     * Store a newly created sales target.
     */
    public function store(SalesTargetRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();
            
            // Check if target already exists for this user/period/type
            $existingTarget = $this->salesTargetRepository->findWhere([
                'user_id' => $data['user_id'],
                'financial_year' => $data['financial_year'],
                'period_type' => $data['period_type'],
                'quarter' => $data['quarter'] ?? null,
                'month' => $data['month'] ?? null,
                'target_type' => $data['target_type'],
            ])->first();

            if ($existingTarget) {
                session()->flash('error', trans('sales::app.targets.target-already-exists'));
                return redirect()->back()->withInput();
            }

            $target = $this->salesTargetRepository->create($data);

            session()->flash('success', trans('sales::app.targets.create-success'));

            return redirect()->route('admin.sales.targets.index');
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show the form for editing a sales target.
     */
    public function edit(int $id): View
    {
        $target = $this->salesTargetRepository->findOrFail($id);
        $users = $this->userRepository->all(['id', 'name']);
        
        return view('sales::targets.edit', compact('target', 'users'));
    }

    /**
     * Update the specified sales target.
     */
    public function update(SalesTargetRequest $request, int $id): RedirectResponse
    {
        try {
            $data = $request->validated();
            
            // Check if target already exists for this user/period/type (excluding current)
            $existingTarget = $this->salesTargetRepository->findWhere([
                'user_id' => $data['user_id'],
                'financial_year' => $data['financial_year'],
                'period_type' => $data['period_type'],
                'quarter' => $data['quarter'] ?? null,
                'month' => $data['month'] ?? null,
                'target_type' => $data['target_type'],
            ])->where('id', '!=', $id)->first();

            if ($existingTarget) {
                session()->flash('error', trans('sales::app.targets.target-already-exists'));
                return redirect()->back()->withInput();
            }

            $target = $this->salesTargetRepository->update($data, $id);

            session()->flash('success', trans('sales::app.targets.update-success'));

            return redirect()->route('admin.sales.targets.index');
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified sales target.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $target = $this->salesTargetRepository->findOrFail($id);
            
            // Check if target has achievements
            if ($target->achievements()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => trans('sales::app.targets.cannot-delete-with-achievements')
                ], 400);
            }

            $this->salesTargetRepository->delete($id);

            return response()->json([
                'success' => true,
                'message' => trans('sales::app.targets.delete-success')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk create targets for multiple users.
     */
    public function bulkCreate(Request $request): RedirectResponse
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'financial_year' => 'required|string',
            'period_type' => 'required|in:annual,quarterly,monthly',
            'target_type' => 'required|in:revenue,leads,deals',
            'target_value' => 'required|numeric|min:0',
            'quarter' => 'nullable|integer|between:1,4',
            'month' => 'nullable|integer|between:1,12',
        ]);

        try {
            $created = 0;
            $skipped = 0;

            foreach ($request->user_ids as $userId) {
                // Check if target already exists
                $existingTarget = $this->salesTargetRepository->findWhere([
                    'user_id' => $userId,
                    'financial_year' => $request->financial_year,
                    'period_type' => $request->period_type,
                    'quarter' => $request->quarter,
                    'month' => $request->month,
                    'target_type' => $request->target_type,
                ])->first();

                if ($existingTarget) {
                    $skipped++;
                    continue;
                }

                $this->salesTargetRepository->create([
                    'user_id' => $userId,
                    'financial_year' => $request->financial_year,
                    'period_type' => $request->period_type,
                    'quarter' => $request->quarter,
                    'month' => $request->month,
                    'target_type' => $request->target_type,
                    'target_value' => $request->target_value,
                    'status' => 'active',
                ]);

                $created++;
            }

            $message = trans('sales::app.targets.bulk-create-success', [
                'created' => $created,
                'skipped' => $skipped
            ]);

            session()->flash('success', $message);

            return redirect()->route('admin.sales.targets.index');
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
            return redirect()->back()->withInput();
        }
    }
}
