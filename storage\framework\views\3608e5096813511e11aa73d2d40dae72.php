<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        <?php echo app('translator')->get('admin::app.leads.view.title', ['title' => $lead->title]); ?>
     <?php $__env->endSlot(); ?>

    <!-- Content -->
    <div class="relative flex gap-4">
        <!-- Left Panel -->
        <?php echo view_render_event('admin.leads.view.left.before', ['lead' => $lead]); ?>


        <div class="sticky top-[73px] flex min-w-[394px] max-w-[394px] flex-col self-start rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900">
            <!-- Lead Information -->
            <div class="flex w-full flex-col gap-2 border-b border-gray-200 p-4 dark:border-gray-800">
                <!-- Breadcrums -->
                <div class="flex items-center justify-between">
                    <?php if (isset($component)) { $__componentOriginal477735b45b070062c5df1d72c43d48f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal477735b45b070062c5df1d72c43d48f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.breadcrumbs.index','data' => ['name' => 'leads.view','entity' => $lead]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'leads.view','entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lead)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $attributes = $__attributesOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__attributesOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $component = $__componentOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__componentOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
                </div>

                <div class="mb-2">
                    <?php if(($days = $lead->rotten_days) > 0): ?>
                        <?php
                            $lead->tags->prepend([
                                'name'  => '<span class="icon-rotten text-base"></span>' . trans('admin::app.leads.view.rotten-days', ['days' => $days]),
                                'color' => '#FEE2E2'
                            ]);
                        ?>
                    <?php endif; ?>

                    <?php echo view_render_event('admin.leads.view.tags.before', ['lead' => $lead]); ?>


                    <!-- Tags -->
                    <?php if (isset($component)) { $__componentOriginalf851be63606bb172aaceed482091e22c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf851be63606bb172aaceed482091e22c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.tags.index','data' => ['attachEndpoint' => route('admin.leads.tags.attach', $lead->id),'detachEndpoint' => route('admin.leads.tags.detach', $lead->id),'addedTags' => $lead->tags]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::tags'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['attach-endpoint' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.leads.tags.attach', $lead->id)),'detach-endpoint' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.leads.tags.detach', $lead->id)),'added-tags' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lead->tags)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf851be63606bb172aaceed482091e22c)): ?>
<?php $attributes = $__attributesOriginalf851be63606bb172aaceed482091e22c; ?>
<?php unset($__attributesOriginalf851be63606bb172aaceed482091e22c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf851be63606bb172aaceed482091e22c)): ?>
<?php $component = $__componentOriginalf851be63606bb172aaceed482091e22c; ?>
<?php unset($__componentOriginalf851be63606bb172aaceed482091e22c); ?>
<?php endif; ?>

                    <?php echo view_render_event('admin.leads.view.tags.after', ['lead' => $lead]); ?>

                </div>


                <?php echo view_render_event('admin.leads.view.title.before', ['lead' => $lead]); ?>


                <!-- Title -->
                <h3 class="text-lg font-bold dark:text-white">
                    <?php echo e($lead->title); ?>

                </h1>

                <?php echo view_render_event('admin.leads.view.title.after', ['lead' => $lead]); ?>


                <!-- Activity Actions -->
                <div class="flex flex-wrap gap-2">
                    <?php echo view_render_event('admin.leads.view.actions.before', ['lead' => $lead]); ?>


                    <?php if(bouncer()->hasPermission('mail.compose')): ?>
                        <!-- Mail Activity Action -->
                        <?php if (isset($component)) { $__componentOriginal6e7e62b7e8fed3be26d9a026b4495e9a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6e7e62b7e8fed3be26d9a026b4495e9a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.actions.mail','data' => ['entity' => $lead,'entityControlName' => 'lead_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities.actions.mail'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lead),'entity-control-name' => 'lead_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6e7e62b7e8fed3be26d9a026b4495e9a)): ?>
<?php $attributes = $__attributesOriginal6e7e62b7e8fed3be26d9a026b4495e9a; ?>
<?php unset($__attributesOriginal6e7e62b7e8fed3be26d9a026b4495e9a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6e7e62b7e8fed3be26d9a026b4495e9a)): ?>
<?php $component = $__componentOriginal6e7e62b7e8fed3be26d9a026b4495e9a; ?>
<?php unset($__componentOriginal6e7e62b7e8fed3be26d9a026b4495e9a); ?>
<?php endif; ?>
                    <?php endif; ?>

                    <?php if(bouncer()->hasPermission('activities.create')): ?>
                        <!-- File Activity Action -->
                        <?php if (isset($component)) { $__componentOriginale7ed32b182ae3e00a04d1065a8a2ff86 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale7ed32b182ae3e00a04d1065a8a2ff86 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.actions.file','data' => ['entity' => $lead,'entityControlName' => 'lead_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities.actions.file'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lead),'entity-control-name' => 'lead_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale7ed32b182ae3e00a04d1065a8a2ff86)): ?>
<?php $attributes = $__attributesOriginale7ed32b182ae3e00a04d1065a8a2ff86; ?>
<?php unset($__attributesOriginale7ed32b182ae3e00a04d1065a8a2ff86); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale7ed32b182ae3e00a04d1065a8a2ff86)): ?>
<?php $component = $__componentOriginale7ed32b182ae3e00a04d1065a8a2ff86; ?>
<?php unset($__componentOriginale7ed32b182ae3e00a04d1065a8a2ff86); ?>
<?php endif; ?>

                        <!-- Note Activity Action -->
                        <?php if (isset($component)) { $__componentOriginal8bdd483ce4bdd8186f2e725c84d6fd85 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8bdd483ce4bdd8186f2e725c84d6fd85 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.actions.note','data' => ['entity' => $lead,'entityControlName' => 'lead_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities.actions.note'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lead),'entity-control-name' => 'lead_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8bdd483ce4bdd8186f2e725c84d6fd85)): ?>
<?php $attributes = $__attributesOriginal8bdd483ce4bdd8186f2e725c84d6fd85; ?>
<?php unset($__attributesOriginal8bdd483ce4bdd8186f2e725c84d6fd85); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8bdd483ce4bdd8186f2e725c84d6fd85)): ?>
<?php $component = $__componentOriginal8bdd483ce4bdd8186f2e725c84d6fd85; ?>
<?php unset($__componentOriginal8bdd483ce4bdd8186f2e725c84d6fd85); ?>
<?php endif; ?>

                        <!-- Activity Action -->
                        <?php if (isset($component)) { $__componentOriginalc39954a677175d0d994f44af8c16faaf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc39954a677175d0d994f44af8c16faaf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.actions.activity','data' => ['entity' => $lead,'entityControlName' => 'lead_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities.actions.activity'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['entity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lead),'entity-control-name' => 'lead_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc39954a677175d0d994f44af8c16faaf)): ?>
<?php $attributes = $__attributesOriginalc39954a677175d0d994f44af8c16faaf; ?>
<?php unset($__attributesOriginalc39954a677175d0d994f44af8c16faaf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc39954a677175d0d994f44af8c16faaf)): ?>
<?php $component = $__componentOriginalc39954a677175d0d994f44af8c16faaf; ?>
<?php unset($__componentOriginalc39954a677175d0d994f44af8c16faaf); ?>
<?php endif; ?>
                    <?php endif; ?>

                    <?php echo view_render_event('admin.leads.view.actions.after', ['lead' => $lead]); ?>

                </div>
            </div>
            
            <!-- Lead Attributes -->
            <?php echo $__env->make('admin::leads.view.attributes', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <!-- Contact Person -->
            <?php echo $__env->make('admin::leads.view.person', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>

        <?php echo view_render_event('admin.leads.view.left.after', ['lead' => $lead]); ?>


        <?php echo view_render_event('admin.leads.view.right.before', ['lead' => $lead]); ?>

        
        <!-- Right Panel -->
        <div class="flex w-full flex-col gap-4 rounded-lg">
            <!-- Stages Navigation -->
            <?php echo $__env->make('admin::leads.view.stages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <!-- Activities -->
            <?php echo view_render_event('admin.leads.view.activities.before', ['lead' => $lead]); ?>


            <?php if (isset($component)) { $__componentOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.activities.index','data' => ['endpoint' => route('admin.leads.activities.index', $lead->id),'emailDetachEndpoint' => route('admin.leads.emails.detach', $lead->id),'extraTypes' => [
                    ['name' => 'description', 'label' => trans('admin::app.leads.view.tabs.description')],
                    ['name' => 'products', 'label' => trans('admin::app.leads.view.tabs.products')],
                    ['name' => 'quotes', 'label' => trans('admin::app.leads.view.tabs.quotes')],
                ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::activities'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['endpoint' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.leads.activities.index', $lead->id)),'email-detach-endpoint' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.leads.emails.detach', $lead->id)),'extra-types' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                    ['name' => 'description', 'label' => trans('admin::app.leads.view.tabs.description')],
                    ['name' => 'products', 'label' => trans('admin::app.leads.view.tabs.products')],
                    ['name' => 'quotes', 'label' => trans('admin::app.leads.view.tabs.quotes')],
                ])]); ?>
                <!-- Products -->
                 <?php $__env->slot('products', null, []); ?> 
                    <?php echo $__env->make('admin::leads.view.products', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                 <?php $__env->endSlot(); ?>

                <!-- Quotes -->
                 <?php $__env->slot('quotes', null, []); ?> 
                    <?php echo $__env->make('admin::leads.view.quotes', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                 <?php $__env->endSlot(); ?>

                <!-- Description -->
                 <?php $__env->slot('description', null, []); ?> 
                    <div class="p-4 dark:text-white">
                        <?php echo e($lead->description); ?>

                    </div>
                 <?php $__env->endSlot(); ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173)): ?>
<?php $attributes = $__attributesOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173; ?>
<?php unset($__attributesOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173)): ?>
<?php $component = $__componentOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173; ?>
<?php unset($__componentOriginalbd04a9c4fb9c6cfa5ad6054d2fc88173); ?>
<?php endif; ?>

            <?php echo view_render_event('admin.leads.view.activities.after', ['lead' => $lead]); ?>

        </div>

        <?php echo view_render_event('admin.leads.view.right.after', ['lead' => $lead]); ?>

    </div>    
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/leads/view.blade.php ENDPATH**/ ?>