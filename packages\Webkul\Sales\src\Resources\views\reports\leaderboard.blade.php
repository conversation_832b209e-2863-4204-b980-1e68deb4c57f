@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.reports.leaderboard.title') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.reports.leaderboard.title') }}</h1>
                <p>{{ __('sales::app.reports.leaderboard.description') }}</p>
            </div>
            
            <div class="page-action">
                <a href="{{ route('admin.sales.reports.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Reports
                </a>
            </div>
        </div>

        <div class="page-content">
            @if(isset($topPerformers) && count($topPerformers) > 0)
                <!-- Top 3 Performers -->
                <div class="row mb-4">
                    @foreach($topPerformers->take(3) as $index => $performer)
                        <div class="col-md-4">
                            <div class="card text-center {{ $index == 0 ? 'border-warning' : ($index == 1 ? 'border-info' : 'border-secondary') }}">
                                <div class="card-body">
                                    <div class="position-relative mb-3">
                                        @if($index == 0)
                                            <i class="fas fa-crown fa-3x text-warning"></i>
                                        @elseif($index == 1)
                                            <i class="fas fa-medal fa-3x text-info"></i>
                                        @else
                                            <i class="fas fa-award fa-3x text-secondary"></i>
                                        @endif
                                        <div class="position-absolute top-0 start-0">
                                            <span class="badge badge-{{ $index == 0 ? 'warning' : ($index == 1 ? 'info' : 'secondary') }} rounded-pill">
                                                #{{ $index + 1 }}
                                            </span>
                                        </div>
                                    </div>
                                    <h5 class="card-title">{{ $performer->user_name ?? 'N/A' }}</h5>
                                    <h3 class="text-{{ $index == 0 ? 'warning' : ($index == 1 ? 'info' : 'secondary') }}">
                                        {{ number_format($performer->achievement_percentage, 1) }}%
                                    </h3>
                                    <p class="text-muted mb-2">Achievement Rate</p>
                                    <small class="text-muted">
                                        Target: {{ number_format($performer->target_amount, 0) }}<br>
                                        Actual: {{ number_format($performer->actual_amount, 0) }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Full Leaderboard -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy"></i> Complete Leaderboard
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="60">Rank</th>
                                        <th>Sales Representative</th>
                                        <th>Target Amount</th>
                                        <th>Actual Amount</th>
                                        <th>Achievement %</th>
                                        <th>Performance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($topPerformers as $index => $performer)
                                        <tr class="{{ $index < 3 ? 'table-' . ($index == 0 ? 'warning' : ($index == 1 ? 'info' : 'secondary')) : '' }}">
                                            <td>
                                                <span class="badge badge-{{ $index == 0 ? 'warning' : ($index == 1 ? 'info' : ($index == 2 ? 'secondary' : 'light')) }} rounded-pill">
                                                    #{{ $index + 1 }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($index < 3)
                                                        <i class="fas fa-{{ $index == 0 ? 'crown' : ($index == 1 ? 'medal' : 'award') }} me-2 text-{{ $index == 0 ? 'warning' : ($index == 1 ? 'info' : 'secondary') }}"></i>
                                                    @endif
                                                    <strong>{{ $performer->user_name ?? 'N/A' }}</strong>
                                                </div>
                                            </td>
                                            <td>{{ number_format($performer->target_amount, 2) }}</td>
                                            <td>{{ number_format($performer->actual_amount, 2) }}</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 100px; height: 20px;">
                                                        <div class="progress-bar bg-{{ $performer->achievement_percentage >= 100 ? 'success' : ($performer->achievement_percentage >= 75 ? 'warning' : 'danger') }}" 
                                                             style="width: {{ min($performer->achievement_percentage, 100) }}%">
                                                        </div>
                                                    </div>
                                                    <span class="badge badge-{{ $performer->achievement_percentage >= 100 ? 'success' : ($performer->achievement_percentage >= 75 ? 'warning' : 'danger') }}">
                                                        {{ number_format($performer->achievement_percentage, 1) }}%
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                @if($performer->achievement_percentage >= 100)
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-check"></i> Exceeded
                                                    </span>
                                                @elseif($performer->achievement_percentage >= 90)
                                                    <span class="badge badge-warning">
                                                        <i class="fas fa-star"></i> Excellent
                                                    </span>
                                                @elseif($performer->achievement_percentage >= 75)
                                                    <span class="badge badge-info">
                                                        <i class="fas fa-thumbs-up"></i> Good
                                                    </span>
                                                @else
                                                    <span class="badge badge-danger">
                                                        <i class="fas fa-arrow-up"></i> Needs Improvement
                                                    </span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @else
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                        <h5>No Performance Data</h5>
                        <p class="text-muted">No sales performance data available to display leaderboard.</p>
                        <a href="{{ route('admin.sales.targets.index') }}" class="btn btn-primary">
                            Set Up Targets
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@stop

@push('css')
<style>
    .table-warning {
        background-color: rgba(255, 193, 7, 0.1);
    }
    
    .table-info {
        background-color: rgba(23, 162, 184, 0.1);
    }
    
    .table-secondary {
        background-color: rgba(108, 117, 125, 0.1);
    }
    
    .progress {
        background-color: #e9ecef;
    }
    
    .card.border-warning {
        border-width: 2px;
    }
    
    .card.border-info {
        border-width: 2px;
    }
    
    .card.border-secondary {
        border-width: 2px;
    }
</style>
@endpush
