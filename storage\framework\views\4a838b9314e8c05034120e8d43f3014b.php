<?php echo view_render_event('admin.leads.create.products.form_controls.before'); ?>


<v-product-list :data="products"></v-product-list>

<?php echo view_render_event('admin.leads.create.products.form_controls.after'); ?>


<?php if (! $__env->hasRenderedOnce('d5b96632-2694-4d8f-a520-352af6908d8b')): $__env->markAsRenderedOnce('d5b96632-2694-4d8f-a520-352af6908d8b');
$__env->startPush('scripts'); ?>
    <script
        type="text/x-template"
        id="v-product-list-template"
    >
        <div class="flex flex-col gap-4">
            <?php echo view_render_event('admin.leads.create.products.form_controls.table.before'); ?>


            <!-- Table -->
            <?php if (isset($component)) { $__componentOriginala9dad9f471f1e8ff345be80579eb8136 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9dad9f471f1e8ff345be80579eb8136 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <?php echo view_render_event('admin.leads.create.products.form_controls.table.head.before'); ?>


                <!-- Table Head -->
                <?php if (isset($component)) { $__componentOriginal8ee89c0b398bd7314c2e7815b044fc82 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8ee89c0b398bd7314c2e7815b044fc82 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.thead.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.thead'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if (isset($component)) { $__componentOriginal95a122c91c33f6d66a15a82d7ca67172 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal95a122c91c33f6d66a15a82d7ca67172 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.thead.tr','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.thead.tr'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <?php echo app('translator')->get('admin::app.leads.common.products.product-name'); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => 'text-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'text-center']); ?>
                            <?php echo app('translator')->get('admin::app.leads.common.products.quantity'); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => 'text-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'text-center']); ?>
                            <?php echo app('translator')->get('admin::app.leads.common.products.price'); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => 'text-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'text-center']); ?>
                            <?php echo app('translator')->get('admin::app.leads.common.products.amount'); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => 'text-right']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'text-right']); ?>
                            <?php echo app('translator')->get('admin::app.leads.common.products.action'); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal95a122c91c33f6d66a15a82d7ca67172)): ?>
<?php $attributes = $__attributesOriginal95a122c91c33f6d66a15a82d7ca67172; ?>
<?php unset($__attributesOriginal95a122c91c33f6d66a15a82d7ca67172); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal95a122c91c33f6d66a15a82d7ca67172)): ?>
<?php $component = $__componentOriginal95a122c91c33f6d66a15a82d7ca67172; ?>
<?php unset($__componentOriginal95a122c91c33f6d66a15a82d7ca67172); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8ee89c0b398bd7314c2e7815b044fc82)): ?>
<?php $attributes = $__attributesOriginal8ee89c0b398bd7314c2e7815b044fc82; ?>
<?php unset($__attributesOriginal8ee89c0b398bd7314c2e7815b044fc82); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8ee89c0b398bd7314c2e7815b044fc82)): ?>
<?php $component = $__componentOriginal8ee89c0b398bd7314c2e7815b044fc82; ?>
<?php unset($__componentOriginal8ee89c0b398bd7314c2e7815b044fc82); ?>
<?php endif; ?>

                <?php echo view_render_event('admin.leads.create.products.form_controls.table.head.after'); ?>


                <?php echo view_render_event('admin.leads.create.products.form_controls.table.body.before'); ?>


                <!-- Table Body -->
                <?php if (isset($component)) { $__componentOriginalde01fbd71b7145d08385ea395943e136 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalde01fbd71b7145d08385ea395943e136 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.tbody.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.tbody'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php echo view_render_event('admin.leads.create.products.form_controls.table.body.product_item.before'); ?>


                    <!-- Product Item Vue Component -->
                    <v-product-item
                        v-for='(product, index) in products'
                        :product="product"
                        :key="index"
                        :index="index"
                        @onRemoveProduct="removeProduct($event)"
                    ></v-product-item>

                    <?php echo view_render_event('admin.leads.create.products.form_controls.table.body.product_item.after'); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalde01fbd71b7145d08385ea395943e136)): ?>
<?php $attributes = $__attributesOriginalde01fbd71b7145d08385ea395943e136; ?>
<?php unset($__attributesOriginalde01fbd71b7145d08385ea395943e136); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalde01fbd71b7145d08385ea395943e136)): ?>
<?php $component = $__componentOriginalde01fbd71b7145d08385ea395943e136; ?>
<?php unset($__componentOriginalde01fbd71b7145d08385ea395943e136); ?>
<?php endif; ?>

                <?php echo view_render_event('admin.leads.create.products.form_controls.table.body.after'); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9dad9f471f1e8ff345be80579eb8136)): ?>
<?php $attributes = $__attributesOriginala9dad9f471f1e8ff345be80579eb8136; ?>
<?php unset($__attributesOriginala9dad9f471f1e8ff345be80579eb8136); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9dad9f471f1e8ff345be80579eb8136)): ?>
<?php $component = $__componentOriginala9dad9f471f1e8ff345be80579eb8136; ?>
<?php unset($__componentOriginala9dad9f471f1e8ff345be80579eb8136); ?>
<?php endif; ?>

            <?php echo view_render_event('admin.leads.create.products.form_controls.table.after'); ?>


            <!-- Add New Product Item -->
            <button
                type="button"
                class="flex max-w-max items-center gap-2 text-brandColor"
                @click="addProduct"
            >
                <i class="icon-add text-md !text-brandColor"></i>

                <?php echo app('translator')->get('admin::app.leads.common.products.add-more'); ?>
            </button>
        </div>
    </script>

    <script
        type="text/x-template"
        id="v-product-item-template"
    >
        <?php if (isset($component)) { $__componentOriginal95a122c91c33f6d66a15a82d7ca67172 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal95a122c91c33f6d66a15a82d7ca67172 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.thead.tr','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.thead.tr'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <!-- Product Name -->
            <?php if (isset($component)) { $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.td','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.td'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <?php if (isset($component)) { $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.index','data' => ['class' => '!mb-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!mb-0']); ?>
                    <?php if (isset($component)) { $__componentOriginal47ddd4958c1891eaeb228b5253e37cdf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal47ddd4958c1891eaeb228b5253e37cdf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.lookup.index','data' => [':src' => 'src',':name' => '`${inputName}[name]`',':params' => 'params','placeholder' => trans('admin::app.leads.common.products.product-name'),'@onSelected' => '(product) => addProduct(product)',':value' => '{ id: product.product_id, name: product.name }']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::lookup'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':src' => 'src',':name' => '`${inputName}[name]`',':params' => 'params','placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.common.products.product-name')),'@on-selected' => '(product) => addProduct(product)',':value' => '{ id: product.product_id, name: product.name }']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal47ddd4958c1891eaeb228b5253e37cdf)): ?>
<?php $attributes = $__attributesOriginal47ddd4958c1891eaeb228b5253e37cdf; ?>
<?php unset($__attributesOriginal47ddd4958c1891eaeb228b5253e37cdf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal47ddd4958c1891eaeb228b5253e37cdf)): ?>
<?php $component = $__componentOriginal47ddd4958c1891eaeb228b5253e37cdf; ?>
<?php unset($__componentOriginal47ddd4958c1891eaeb228b5253e37cdf); ?>
<?php endif; ?>

                    <?php if (isset($component)) { $__componentOriginal53af403f6b2179a3039d488b8ab2a267 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53af403f6b2179a3039d488b8ab2a267 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.control','data' => ['type' => 'hidden',':name' => '`${inputName}[product_id]`','vModel' => 'product.product_id','rules' => 'required','label' => trans('admin::app.leads.common.products.product-name'),'placeholder' => trans('admin::app.leads.common.products.product-name')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.control'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'hidden',':name' => '`${inputName}[product_id]`','v-model' => 'product.product_id','rules' => 'required','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.common.products.product-name')),'placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.common.products.product-name'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $attributes = $__attributesOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $component = $__componentOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__componentOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>

                    <?php if (isset($component)) { $__componentOriginal8da25fb6534e2ef288914e35c32417f8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8da25fb6534e2ef288914e35c32417f8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.error','data' => [':name' => '`${inputName}[product_id]`']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':name' => '`${inputName}[product_id]`']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8da25fb6534e2ef288914e35c32417f8)): ?>
<?php $attributes = $__attributesOriginal8da25fb6534e2ef288914e35c32417f8; ?>
<?php unset($__attributesOriginal8da25fb6534e2ef288914e35c32417f8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8da25fb6534e2ef288914e35c32417f8)): ?>
<?php $component = $__componentOriginal8da25fb6534e2ef288914e35c32417f8; ?>
<?php unset($__componentOriginal8da25fb6534e2ef288914e35c32417f8); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $attributes = $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $component = $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $attributes = $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $component = $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>

            <!-- Product Quantity -->
            <?php if (isset($component)) { $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.td','data' => ['class' => 'text-right']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.td'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'text-right']); ?>
                <?php if (isset($component)) { $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if (isset($component)) { $__componentOriginal53af403f6b2179a3039d488b8ab2a267 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53af403f6b2179a3039d488b8ab2a267 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.control','data' => ['type' => 'inline',':name' => '`${inputName}[quantity]`',':value' => 'product.quantity','rules' => 'required|decimal:4','label' => trans('admin::app.leads.common.products.quantity'),'placeholder' => trans('admin::app.leads.common.products.quantity'),'@onChange' => '(event) => product.quantity = event.value','position' => 'center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.control'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'inline',':name' => '`${inputName}[quantity]`',':value' => 'product.quantity','rules' => 'required|decimal:4','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.common.products.quantity')),'placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.common.products.quantity')),'@on-change' => '(event) => product.quantity = event.value','position' => 'center']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $attributes = $__attributesOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $component = $__componentOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__componentOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $attributes = $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $component = $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $attributes = $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $component = $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>

            <!-- Price -->
            <?php if (isset($component)) { $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.td','data' => ['class' => 'text-right']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.td'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'text-right']); ?>
                <?php if (isset($component)) { $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if (isset($component)) { $__componentOriginal53af403f6b2179a3039d488b8ab2a267 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53af403f6b2179a3039d488b8ab2a267 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.control','data' => ['type' => 'inline',':name' => '`${inputName}[price]`',':value' => 'product.price','rules' => 'required|decimal:4','label' => trans('admin::app.leads.common.products.price'),'placeholder' => trans('admin::app.leads.common.products.price'),'@onChange' => '(event) => product.price = event.value',':valueLabel' => '$admin.formatPrice(product.price)','position' => 'center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.control'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'inline',':name' => '`${inputName}[price]`',':value' => 'product.price','rules' => 'required|decimal:4','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.common.products.price')),'placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.common.products.price')),'@on-change' => '(event) => product.price = event.value',':value-label' => '$admin.formatPrice(product.price)','position' => 'center']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $attributes = $__attributesOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $component = $__componentOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__componentOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $attributes = $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $component = $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $attributes = $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $component = $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>

            <!-- Total -->
            <?php if (isset($component)) { $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.td','data' => ['class' => 'text-right']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.td'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'text-right']); ?>
                <?php if (isset($component)) { $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if (isset($component)) { $__componentOriginal53af403f6b2179a3039d488b8ab2a267 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53af403f6b2179a3039d488b8ab2a267 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.control','data' => ['type' => 'inline',':name' => '`${inputName}[amount]`',':value' => 'product.price * product.quantity','rules' => 'required|decimal:4','label' => trans('admin::app.leads.common.products.total'),'placeholder' => trans('admin::app.leads.common.products.total'),':allowEdit' => 'false','position' => 'center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.control'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'inline',':name' => '`${inputName}[amount]`',':value' => 'product.price * product.quantity','rules' => 'required|decimal:4','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.common.products.total')),'placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('admin::app.leads.common.products.total')),':allowEdit' => 'false','position' => 'center']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $attributes = $__attributesOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__attributesOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53af403f6b2179a3039d488b8ab2a267)): ?>
<?php $component = $__componentOriginal53af403f6b2179a3039d488b8ab2a267; ?>
<?php unset($__componentOriginal53af403f6b2179a3039d488b8ab2a267); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $attributes = $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $component = $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $attributes = $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $component = $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>

            <!-- Action -->
            <?php if (isset($component)) { $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.td','data' => ['class' => 'text-right']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::table.td'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'text-right']); ?>
                <?php if (isset($component)) { $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <i
                        @click="removeProduct"
                        class="icon-delete cursor-pointer text-2xl"
                    ></i>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $attributes = $__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__attributesOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3)): ?>
<?php $component = $__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3; ?>
<?php unset($__componentOriginal7b1bc76a00ab5e7f1bf2c6429dae85a3); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $attributes = $__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__attributesOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc)): ?>
<?php $component = $__componentOriginal7bda9cdc3924faf4607e2df004a89fbc; ?>
<?php unset($__componentOriginal7bda9cdc3924faf4607e2df004a89fbc); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal95a122c91c33f6d66a15a82d7ca67172)): ?>
<?php $attributes = $__attributesOriginal95a122c91c33f6d66a15a82d7ca67172; ?>
<?php unset($__attributesOriginal95a122c91c33f6d66a15a82d7ca67172); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal95a122c91c33f6d66a15a82d7ca67172)): ?>
<?php $component = $__componentOriginal95a122c91c33f6d66a15a82d7ca67172; ?>
<?php unset($__componentOriginal95a122c91c33f6d66a15a82d7ca67172); ?>
<?php endif; ?>
    </script>

    <script type="module">
        app.component('v-product-list', {
            template: '#v-product-list-template',

            props: ['data'],

            data: function () {
                return {
                    products: this.data ? this.data : [],
                }
            },

            methods: {
                addProduct() {
                    this.products.push({
                        id: null,
                        product_id: null,
                        name: '',
                        quantity: 1,
                        price: 0,
                        amount: null,
                    })
                },

                removeProduct (product) {
                    const index = this.products.indexOf(product);
                    this.products.splice(index, 1);
                },
            },
        });

        app.component('v-product-item', {
            template: '#v-product-item-template',

            props: ['index', 'product'],

            data() {
                return {
                    products: [],
                }
            },

            computed: {
                inputName() {
                    if (this.product.id) {
                        return "products[" + this.product.id + "]";
                    }

                    return "products[product_" + this.index + "]";
                },

                src() {
                    return '<?php echo e(route('admin.products.search')); ?>';
                },

                params() {
                    return {
                        params: {
                            query: this.product.name,
                        },
                    };
                },
            },

            methods: {
                /**
                 * Add the product.
                 *
                 * @param {Object} result
                 *
                 * @return {void}
                 */
                addProduct(result) {
                    this.product.product_id = result.id;

                    this.product.name = result.name;

                    this.product.price = result.price;

                    this.product.quantity = result.quantity ?? 1;

                    // Load custom attributes from the product
                    if (result.custom_attributes) {
                        for (let key in result.custom_attributes) {
                            this.product[key] = result.custom_attributes[key];
                        }
                    }
                },

                /**
                 * Remove the product.
                 *
                 * @return {void}
                 */
                removeProduct () {
                    this.$emit('onRemoveProduct', this.product)
                }
            }
        });
    </script>
<?php $__env->stopPush(); endif; ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/leads/common/products.blade.php ENDPATH**/ ?>