<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Webkul\User\Models\Role;
use Webkul\User\Models\User;

class TestUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $role = Role::first();

        if (! $role) {
            echo "No roles found. Please create roles first.\n";

            return;
        }

        $users = [
            ['name' => 'John Sales', 'email' => '<EMAIL>'],
            ['name' => 'Jane Marketing', 'email' => '<EMAIL>'],
            ['name' => 'Bob Manager', 'email' => '<EMAIL>'],
            ['name' => 'Alice Lead', 'email' => '<EMAIL>'],
        ];

        foreach ($users as $userData) {
            $existingUser = User::where('email', $userData['email'])->first();

            if (! $existingUser) {
                User::create([
                    'name'     => $userData['name'],
                    'email'    => $userData['email'],
                    'password' => bcrypt('password'),
                    'role_id'  => $role->id,
                    'status'   => 1,
                ]);
                echo 'Created user: '.$userData['name']."\n";
            } else {
                echo 'User already exists: '.$userData['name']."\n";
            }
        }
    }
}
