@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.reports.target-vs-actual.title') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.reports.target-vs-actual.title') }}</h1>
                <p>{{ __('sales::app.reports.target-vs-actual.description') }}</p>
            </div>
            
            <div class="page-action">
                <a href="{{ route('admin.sales.reports.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Reports
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="page-content">
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.sales.reports.target_vs_actual') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="financial_year">Financial Year</label>
                                    <select name="financial_year" id="financial_year" class="form-control">
                                        <option value="">All Years</option>
                                        @for($year = date('Y'); $year >= date('Y') - 5; $year--)
                                            <option value="{{ $year }}" {{ ($filters['financial_year'] ?? '') == $year ? 'selected' : '' }}>
                                                {{ $year }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="period_type">Period Type</label>
                                    <select name="period_type" id="period_type" class="form-control">
                                        <option value="">All Periods</option>
                                        <option value="quarter" {{ ($filters['period_type'] ?? '') == 'quarter' ? 'selected' : '' }}>Quarter</option>
                                        <option value="month" {{ ($filters['period_type'] ?? '') == 'month' ? 'selected' : '' }}>Month</option>
                                        <option value="half_year" {{ ($filters['period_type'] ?? '') == 'half_year' ? 'selected' : '' }}>Half Year</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="user_id">Sales Representative</label>
                                    <select name="user_id" id="user_id" class="form-control">
                                        <option value="">All Users</option>
                                        @if(isset($users))
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}" {{ ($filters['user_id'] ?? '') == $user->id ? 'selected' : '' }}>
                                                    {{ $user->name }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter"></i> Apply Filters
                                        </button>
                                        <a href="{{ route('admin.sales.reports.target_vs_actual') }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Cards -->
            @if(isset($reportData) && count($reportData) > 0)
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>{{ number_format($reportData->sum('target_amount'), 0) }}</h4>
                                        <p class="mb-0">Total Target</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-bullseye fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>{{ number_format($reportData->sum('actual_amount'), 0) }}</h4>
                                        <p class="mb-0">Total Actual</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-line fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>{{ number_format($reportData->avg('achievement_percentage'), 1) }}%</h4>
                                        <p class="mb-0">Avg Achievement</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-percentage fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>{{ $reportData->where('achievement_percentage', '>=', 100)->count() }}</h4>
                                        <p class="mb-0">Targets Met</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-trophy fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Performance Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sales Rep</th>
                                        <th>Period</th>
                                        <th>Target Amount</th>
                                        <th>Actual Amount</th>
                                        <th>Achievement %</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($reportData as $data)
                                        <tr>
                                            <td>{{ $data->user_name ?? 'N/A' }}</td>
                                            <td>{{ $data->period_type }} {{ $data->period_value }}, {{ $data->financial_year }}</td>
                                            <td>{{ number_format($data->target_amount, 2) }}</td>
                                            <td>{{ number_format($data->actual_amount, 2) }}</td>
                                            <td>
                                                <span class="badge badge-{{ $data->achievement_percentage >= 100 ? 'success' : ($data->achievement_percentage >= 75 ? 'warning' : 'danger') }}">
                                                    {{ number_format($data->achievement_percentage, 1) }}%
                                                </span>
                                            </td>
                                            <td>
                                                @if($data->achievement_percentage >= 100)
                                                    <span class="badge badge-success">Target Met</span>
                                                @elseif($data->achievement_percentage >= 75)
                                                    <span class="badge badge-warning">Close</span>
                                                @else
                                                    <span class="badge badge-danger">Below Target</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @else
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5>No Data Available</h5>
                        <p class="text-muted">No performance data found for the selected criteria.</p>
                        <a href="{{ route('admin.sales.targets.index') }}" class="btn btn-primary">
                            Set Up Targets
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@stop
