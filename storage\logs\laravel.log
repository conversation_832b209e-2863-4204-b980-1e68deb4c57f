[2025-04-18 21:08:39] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\composer\\ClassLoader.php:429)
[stacktrace]
#0 {main}
"} 
[2025-05-01 00:33:46] local.INFO: LeadData before custom attributes: {"title":"Augment Sample Lead Title","description":"This is a sample lead description","lead_value":8000,"status":1,"expected_close_date":45762,"person_name":"<PERSON><PERSON> shaw","user_name":"Example Admin","source_name":"Website","type_name":"New Business","pipeline_name":"Default Pipeline","stage_name":"Prospect","person_id":16,"lead_source_id":6,"lead_type_id":1,"lead_pipeline_id":1,"lead_pipeline_stage_id":3,"user_id":1} 
[2025-05-01 00:33:46] local.INFO: All Attributes being processed: ["lead_value","lead_source_id","lead_type_id","user_id","expected_close_date","lead_pipeline_id","lead_pipeline_stage_id"] 
[2025-05-01 00:33:46] local.INFO: LeadData before custom attributes: {"title":"Gemini 2.5 pro","description":"This is a sample lead description","lead_value":8000,"status":1,"expected_close_date":45762,"person_name":"Aman shaw","user_name":"Example Admin","source_name":"Website","type_name":"New Business","pipeline_name":"Default Pipeline","stage_name":"Prospect","person_id":18,"lead_source_id":6,"lead_type_id":1,"lead_pipeline_id":1,"lead_pipeline_stage_id":3,"user_id":1} 
[2025-05-01 00:33:46] local.INFO: All Attributes being processed: ["lead_value","lead_source_id","lead_type_id","user_id","expected_close_date","lead_pipeline_id","lead_pipeline_stage_id"] 
[2025-06-30 23:09:13] local.ERROR: Class "Webkul\Sales\Providers\SalesServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Webkul\\Sales\\Providers\\SalesServiceProvider\" not found at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(771): Illuminate\\Foundation\\ProviderRepository->load()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#8 {main}
"} 
[2025-06-30 23:13:59] local.ERROR: Class "Webkul\Sales\Models\SalesTargetProxy" not found {"exception":"[object] (Error(code: 0): Class \"Webkul\\Sales\\Models\\SalesTargetProxy\" not found at C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\konekt\\concord\\src\\Concord.php:269)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\konekt\\concord\\src\\Concord.php(145): Konekt\\Concord\\Concord->resetProxy()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\konekt\\concord\\src\\BaseServiceProvider.php(262): Konekt\\Concord\\Concord->registerModel()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\konekt\\concord\\src\\BaseServiceProvider.php(98): Konekt\\Concord\\BaseServiceProvider->registerModels()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Konekt\\Concord\\BaseServiceProvider->boot()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1036): Illuminate\\Container\\Container->call()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1017): Illuminate\\Foundation\\Application->bootProvider()
#10 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1016): array_walk()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#17 {main}
"} 
