<?php if (isset($component)) { $__componentOriginal2e811256b7438d8e1a8b356c08ce1fd4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2e811256b7438d8e1a8b356c08ce1fd4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.control-group.controls.inline.phone','data' => [':name' => '\''.e($attribute->code).'\'','value' => $value,'rules' => 'required|decimal:4','position' => 'left','label' => $attribute->name,':errors' => 'errors','placeholder' => $attribute->name,'url' => $url,'allowEdit' => $allowEdit]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form.control-group.controls.inline.phone'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':name' => '\''.e($attribute->code).'\'','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($value),'rules' => 'required|decimal:4','position' => 'left','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attribute->name),':errors' => 'errors','placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attribute->name),'url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($url),'allow-edit' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allowEdit)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2e811256b7438d8e1a8b356c08ce1fd4)): ?>
<?php $attributes = $__attributesOriginal2e811256b7438d8e1a8b356c08ce1fd4; ?>
<?php unset($__attributesOriginal2e811256b7438d8e1a8b356c08ce1fd4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2e811256b7438d8e1a8b356c08ce1fd4)): ?>
<?php $component = $__componentOriginal2e811256b7438d8e1a8b356c08ce1fd4; ?>
<?php unset($__componentOriginal2e811256b7438d8e1a8b356c08ce1fd4); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/components/attributes/view/phone.blade.php ENDPATH**/ ?>