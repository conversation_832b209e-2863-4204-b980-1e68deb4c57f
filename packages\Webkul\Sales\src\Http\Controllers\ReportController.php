<?php

namespace Webkul\Sales\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\Response;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Sales\Repositories\SalesTargetRepository;
use Webkul\User\Repositories\UserRepository;

class ReportController extends Controller
{
    public function __construct(
        protected SalesTargetRepository $salesTargetRepository,
        protected UserRepository $userRepository
    ) {
    }

    /**
     * Display reports index page.
     */
    public function index(): View
    {
        return view('sales::reports.index');
    }

    /**
     * Target vs Actual report.
     */
    public function targetVsActual(Request $request): View
    {
        $filters = $request->only(['financial_year', 'period_type', 'user_id']);
        
        $reportData = $this->salesTargetRepository->getPerformanceSummary($filters);
        
        return view('sales::reports.target-vs-actual', compact('reportData', 'filters'));
    }

    /**
     * Leaderboard report.
     */
    public function leaderboard(Request $request): View
    {
        $financialYear = $request->get('financial_year');
        
        $topPerformers = $this->salesTargetRepository
            ->with('user')
            ->when($financialYear, function ($query, $year) {
                return $query->where('financial_year', $year);
            })
            ->orderBy('achievement_percentage', 'desc')
            ->limit(20)
            ->get();

        return view('sales::reports.leaderboard', compact('topPerformers'));
    }

    /**
     * Forecast report.
     */
    public function forecast(): View
    {
        // Implementation for forecast report
        return view('sales::reports.forecast');
    }

    /**
     * Export reports.
     */
    public function export(string $type): Response
    {
        // Implementation for export functionality
        return response()->json(['message' => 'Export functionality coming soon']);
    }
}
