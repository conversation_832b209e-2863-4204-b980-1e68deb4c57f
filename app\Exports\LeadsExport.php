<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Webkul\Lead\Repositories\LeadRepository;

class LeadsExport implements FromCollection, WithHeadings
{
    /**
     * Create a new exporter instance.
     */
    public function __construct(protected LeadRepository $leadRepository)
    {
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = $this->leadRepository->with([
            'user',
            'person',
            'source',
            'type',
            'stage',
            'activities'
        ]);

        $filter = request()->get('filter', 'all');

        switch ($filter) {
            case 'weekly':
                $query->where('created_at', '>=', now()->startOfWeek())
                      ->where('created_at', '<=', now()->endOfWeek());
                break;
            case 'monthly':
                $query->where('created_at', '>=', now()->startOfMonth())
                      ->where('created_at', '<=', now()->endOfMonth());
                break;
            case 'quarterly':
                $query->where('created_at', '>=', now()->startOfQuarter())
                      ->where('created_at', '<=', now()->endOfQuarter());
                break;
            case 'annually':
                $query->where('created_at', '>=', now()->startOfYear())
                      ->where('created_at', '<=', now()->endOfYear());
                break;
            case 'custom':
                $startDate = request()->get('start_date');
                $endDate = request()->get('end_date');
                if ($startDate && $endDate) {
                    $query->where('created_at', '>=', $startDate . ' 00:00:00')
                          ->where('created_at', '<=', $endDate . ' 23:59:59');
                }
                break;
        }

        $leads = $query->all();

        return $leads->map(function ($lead) {
            $latestActivity = $lead->activities->sortByDesc('created_at')->first();
            
            return [
                'id' => $lead->id,
                'title' => $lead->title,
                'value' => core()->formatBasePrice($lead->lead_value),
                'contact_person' => $lead->person?->name ?? 'N/A',
                'sales_person' => $lead->user?->name ?? 'N/A',
                'source' => $lead->source?->name ?? 'N/A',
                'type' => $lead->type?->name ?? 'N/A',
                'stage' => $lead->stage?->name ?? 'N/A',
                'status' => $lead->status ? 'Active' : 'Inactive',
                'description' => $lead->description ?? 'N/A',
                'lost_reason' => $lead->lost_reason ?? 'N/A',
                'expected_close_date' => $lead->expected_close_date ? $lead->expected_close_date->format('Y-m-d') : 'N/A',
                'closed_at' => $lead->closed_at ? $lead->closed_at->format('Y-m-d H:i:s') : 'N/A',
                'latest_activity' => $latestActivity ? ($latestActivity->comment ?? $latestActivity->title ?? 'Activity') . ' (' . $latestActivity->created_at->format('Y-m-d H:i:s') . ')' : 'No Interaction',
                'created_at' => $lead->created_at?->format('Y-m-d H:i:s') ?? 'N/A',
                'updated_at' => $lead->updated_at?->format('Y-m-d H:i:s') ?? 'N/A',
            ];
        });
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Title',
            'Value',
            'Contact Person',
            'Sales Person', 
            'Source',
            'Type',
            'Stage',
            'Status',
            'Description',
            'Lost Reason',
            'Expected Close Date',
            'Closed At',
            'Latest Activity',
            'Created At',
            'Updated At'
        ];
    }
}