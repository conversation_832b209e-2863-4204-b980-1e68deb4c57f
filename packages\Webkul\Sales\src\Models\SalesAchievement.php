<?php

namespace Webkul\Sales\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Webkul\Sales\Contracts\SalesAchievement as SalesAchievementContract;
use Webkul\User\Models\User;

class SalesAchievement extends Model implements SalesAchievementContract
{
    protected $table = 'crm_sales_achievements';

    protected $fillable = [
        'user_id',
        'sales_target_id',
        'achievement_date',
        'achievement_type',
        'achievement_value',
        'source_type',
        'source_id',
        'description',
        'metadata',
    ];

    protected $casts = [
        'achievement_date'  => 'date',
        'achievement_value' => 'decimal:2',
        'metadata'          => 'array',
    ];

    /**
     * Get the user that owns the achievement.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the sales target this achievement belongs to.
     */
    public function salesTarget(): BelongsTo
    {
        return $this->belongsTo(SalesTarget::class);
    }

    /**
     * Get the source model (Lead, Quote, etc.).
     */
    public function source()
    {
        if (! $this->source_type || ! $this->source_id) {
            return null;
        }

        switch ($this->source_type) {
            case 'lead':
                return $this->belongsTo(\Webkul\Lead\Models\Lead::class, 'source_id');
            case 'quote':
                return $this->belongsTo(\Webkul\Quote\Models\Quote::class, 'source_id');
            default:
                return null;
        }
    }

    /**
     * Scope for specific achievement type.
     */
    public function scopeForAchievementType($query, $type)
    {
        return $query->where('achievement_type', $type);
    }

    /**
     * Scope for specific date range.
     */
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('achievement_date', [$startDate, $endDate]);
    }

    /**
     * Scope for specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
