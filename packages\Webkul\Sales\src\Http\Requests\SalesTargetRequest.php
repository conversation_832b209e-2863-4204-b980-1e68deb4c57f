<?php

namespace Webkul\Sales\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SalesTargetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'user_id' => 'required|exists:users,id',
            'financial_year' => 'required|string|regex:/^\d{4}-\d{2}$/',
            'period_type' => 'required|in:annual,quarterly,monthly',
            'target_type' => 'required|in:revenue,leads,deals',
            'target_value' => 'required|numeric|min:0',
            'status' => 'sometimes|in:active,inactive,completed',
            'notes' => 'nullable|string|max:1000',
        ];

        // Add conditional validation based on period type
        if ($this->period_type === 'quarterly') {
            $rules['quarter'] = 'required|integer|between:1,4';
        }

        if ($this->period_type === 'monthly') {
            $rules['month'] = 'required|integer|between:1,12';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'Please select a user.',
            'user_id.exists' => 'Selected user does not exist.',
            'financial_year.required' => 'Financial year is required.',
            'financial_year.regex' => 'Financial year must be in format YYYY-YY (e.g., 2024-25).',
            'period_type.required' => 'Period type is required.',
            'period_type.in' => 'Period type must be annual, quarterly, or monthly.',
            'target_type.required' => 'Target type is required.',
            'target_type.in' => 'Target type must be revenue, leads, or deals.',
            'target_value.required' => 'Target value is required.',
            'target_value.numeric' => 'Target value must be a number.',
            'target_value.min' => 'Target value must be at least 0.',
            'quarter.required' => 'Quarter is required for quarterly targets.',
            'quarter.between' => 'Quarter must be between 1 and 4.',
            'month.required' => 'Month is required for monthly targets.',
            'month.between' => 'Month must be between 1 and 12.',
            'status.in' => 'Status must be active, inactive, or completed.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default status if not provided
        if (!$this->has('status')) {
            $this->merge(['status' => 'active']);
        }

        // Remove quarter/month if not applicable
        if ($this->period_type !== 'quarterly') {
            $this->request->remove('quarter');
        }

        if ($this->period_type !== 'monthly') {
            $this->request->remove('month');
        }
    }
}
