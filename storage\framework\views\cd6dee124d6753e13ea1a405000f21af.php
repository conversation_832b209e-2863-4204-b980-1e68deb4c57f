<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['leads']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['leads']); ?>
<?php foreach (array_filter((['leads']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="flex flex-col gap-4">
    <div class="relative overflow-auto border dark:border-gray-800 rounded-lg max-h-[calc(100vh-300px)]">
        <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
            
            <thead class="text-xs uppercase bg-gray-50 dark:bg-gray-900 dark:text-gray-400 border-b dark:border-gray-800">
                <tr>
                    
                    <th scope="col" class="sticky left-0 px-6 py-3 bg-gray-50 dark:bg-gray-900 z-10">ID</th>
                    <th scope="col" class="sticky left-[80px] px-6 py-3 bg-gray-50 dark:bg-gray-900 z-10">Title</th>
                    <th scope="col" class="sticky left-[280px] px-6 py-3 bg-gray-50 dark:bg-gray-900 z-10">Value</th>
                    
                    <th scope="col" class="px-6 py-3">Contact Person</th>
                    <th scope="col" class="px-6 py-3">Sales Person</th>
                    <th scope="col" class="px-6 py-3">Source</th>
                    <th scope="col" class="px-6 py-3">Type</th>
                    <th scope="col" class="px-6 py-3">Stage</th>
                    <th scope="col" class="px-6 py-3">Status</th>
                    <th scope="col" class="px-6 py-3">Description</th>
                    <th scope="col" class="px-6 py-3">Lost Reason</th>
                    <th scope="col" class="px-6 py-3">Expected Close Date</th>
                    <th scope="col" class="px-6 py-3">Closed Date</th>
                    <th scope="col" class="px-6 py-3">Latest Activity</th>
                    <th scope="col" class="px-6 py-3">Created At</th>
                    <th scope="col" class="px-6 py-3">Updated At</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $leads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        
                        <td class="sticky left-0 px-6 py-4 bg-white dark:bg-gray-800 z-10"><?php echo e($lead->id); ?></td>
                        <td class="sticky left-[80px] px-6 py-4 bg-white dark:bg-gray-800 z-10"><?php echo e($lead->title); ?></td>
                        <td class="sticky left-[280px] px-6 py-4 bg-white dark:bg-gray-800 z-10"><?php echo e(core()->formatBasePrice($lead->lead_value)); ?></td>
                        
                        <td class="px-6 py-4"><?php echo e($lead->person?->name ?? 'N/A'); ?></td>
                        <td class="px-6 py-4"><?php echo e($lead->user?->name ?? 'N/A'); ?></td>
                        <td class="px-6 py-4"><?php echo e($lead->source?->name ?? 'N/A'); ?></td>
                        <td class="px-6 py-4"><?php echo e($lead->type?->name ?? 'N/A'); ?></td>
                        <td class="px-6 py-4"><?php echo e($lead->stage?->name ?? 'N/A'); ?></td>
                        <td class="px-6 py-4"><?php echo e($lead->status ? 'Active' : 'Inactive'); ?></td>
                        <td class="px-6 py-4 max-w-xs truncate"><?php echo e($lead->description ?? 'N/A'); ?></td>
                        <td class="px-6 py-4"><?php echo e($lead->lost_reason ?? 'N/A'); ?></td>
                        <td class="px-6 py-4"><?php echo e($lead->expected_close_date ? $lead->expected_close_date->format('Y-m-d') : 'N/A'); ?></td>
                        <td class="px-6 py-4"><?php echo e($lead->closed_at ? $lead->closed_at->format('Y-m-d H:i:s') : 'N/A'); ?></td>
                        <td class="px-6 py-4">
                            <?php if($latestActivity = $lead->activities->sortByDesc('created_at')->first()): ?>
                                <?php echo e($latestActivity->comment ?? $latestActivity->title ?? 'Activity'); ?>

                                <span class="block text-xs text-gray-400">(<?php echo e($latestActivity->created_at->format('Y-m-d H:i:s')); ?>)</span>
                            <?php else: ?>
                                No Interaction
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4"><?php echo e($lead->created_at?->format('Y-m-d H:i:s') ?? 'N/A'); ?></td>
                        <td class="px-6 py-4"><?php echo e($lead->updated_at?->format('Y-m-d H:i:s') ?? 'N/A'); ?></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                        <td colspan="16" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400"><?php echo e(__('admin::app.datagrid.no-records')); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    
    <?php if($leads->hasPages()): ?>
        <div class="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 dark:bg-gray-800 dark:border-gray-700">
            <div class="flex justify-between flex-1 sm:hidden">
                <?php if($leads->onFirstPage()): ?>
                    <span class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md dark:text-gray-400 dark:bg-gray-800 dark:border-gray-600">
                        Previous
                    </span>
                <?php else: ?>
                    <a href="<?php echo e($leads->previousPageUrl()); ?>" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:ring ring-gray-300 focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:text-white">
                        Previous
                    </a>
                <?php endif; ?>

                <!-- Go to page input and button for mobile -->
                <div class="flex items-center gap-2 mx-2">
                    <input 
                        type="number" 
                        min="1" 
                        max="<?php echo e($leads->lastPage()); ?>"
                        value="<?php echo e($leads->currentPage()); ?>"
                        class="w-16 rounded-md border border-gray-300 px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
                        onkeypress="goToPage(event, this, <?php echo e($leads->lastPage()); ?>)"
                        id="page-input-mobile"
                    >
                    <button 
                        onclick="goToPageButton(<?php echo e($leads->lastPage()); ?>)"
                        class="px-2 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
                    >
                        Go
                    </button>
                </div>

                <?php if($leads->hasMorePages()): ?>
                    <a href="<?php echo e($leads->nextPageUrl()); ?>" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:ring ring-gray-300 focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:text-white">
                        Next
                    </a>
                <?php else: ?>
                    <span class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md dark:text-gray-400 dark:bg-gray-800 dark:border-gray-600">
                        Next
                    </span>
                <?php endif; ?>
            </div>

            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700 leading-5 dark:text-gray-300">
                        Showing
                        <span class="font-medium"><?php echo e($leads->firstItem()); ?></span>
                        to
                        <span class="font-medium"><?php echo e($leads->lastItem()); ?></span>
                        of
                        <span class="font-medium"><?php echo e($leads->total()); ?></span>
                        results
                    </p>
                </div>

                <div class="flex items-center">
                    <span class="relative z-0 inline-flex shadow-sm rounded-md">
                        
                        <?php if($leads->onFirstPage()): ?>
                            <span aria-disabled="true" aria-label="Previous">
                                <span class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-l-md leading-5 dark:bg-gray-800 dark:border-gray-600" aria-hidden="true">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </span>
                            </span>
                        <?php else: ?>
                            <a href="<?php echo e($leads->previousPageUrl()); ?>" rel="prev" class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:ring ring-gray-300 focus:border-blue-300 active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150 dark:bg-gray-800 dark:border-gray-600 dark:hover:text-gray-300" aria-label="Previous">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        <?php endif; ?>

                        <!-- Go to page input and button -->
                        <div class="flex items-center border-t border-b border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800">
                            <input 
                                type="number" 
                                min="1" 
                                max="<?php echo e($leads->lastPage()); ?>"
                                value="<?php echo e($leads->currentPage()); ?>"
                                class="w-16 px-2 py-1 text-sm border-0 focus:ring-0 focus:outline-none dark:bg-gray-800 dark:text-gray-300"
                                onkeypress="goToPage(event, this, <?php echo e($leads->lastPage()); ?>)"
                                title="Go to page (of <?php echo e($leads->lastPage()); ?>)"
                                id="page-input"
                            >
                            <button 
                                onclick="goToPageButton(<?php echo e($leads->lastPage()); ?>)"
                                class="px-2 py-1 text-sm font-medium text-gray-700 border-l border-gray-300 hover:bg-gray-50 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 focus:outline-none focus:ring-0"
                                title="Go to entered page"
                            >
                                Go
                            </button>
                        </div>

                        
                        <?php if($leads->hasMorePages()): ?>
                            <a href="<?php echo e($leads->nextPageUrl()); ?>" rel="next" class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:ring ring-gray-300 focus:border-blue-300 active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150 dark:bg-gray-800 dark:border-gray-600 dark:hover:text-gray-300" aria-label="Next">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        <?php else: ?>
                            <span aria-disabled="true" aria-label="Next">
                                <span class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-r-md leading-5 dark:bg-gray-800 dark:border-gray-600" aria-hidden="true">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </span>
                            </span>
                        <?php endif; ?>
                    </span>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script>
    function goToPage(event, input, maxPage) {
        if (event.key === 'Enter') {
            navigateToPage(input.value, maxPage);
        }
    }

    function goToPageButton(maxPage) {
        const input = document.getElementById('page-input') || document.getElementById('page-input-mobile');
        navigateToPage(input.value, maxPage);
    }

    function navigateToPage(pageValue, maxPage) {
        let page = parseInt(pageValue);
        if (page < 1) page = 1;
        if (page > maxPage) page = maxPage;
        
        let currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('page', page);
        window.location.href = currentUrl.toString();
    }
    </script>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\resources\views/components/report.blade.php ENDPATH**/ ?>