<?php

namespace Webkul\Sales\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Sales\Repositories\SalesTargetRepository;
use Webkul\User\Repositories\UserRepository;

class ReportController extends Controller
{
    public function __construct(
        protected SalesTargetRepository $salesTargetRepository,
        protected UserRepository $userRepository
    ) {}

    /**
     * Display reports index page.
     */
    public function index(): View
    {
        // Get summary statistics for the dashboard
        $totalTargets = $this->salesTargetRepository->count();
        $activeUsers = $this->userRepository->count();

        // Calculate average achievement percentage
        $avgAchievement = $this->salesTargetRepository->getModel()
            ->selectRaw('AVG(CASE WHEN target_value > 0 THEN (achieved_value / target_value) * 100 ELSE 0 END) as avg_achievement')
            ->value('avg_achievement') ?? 0;

        $currentPeriod = 'Q'.ceil(date('n') / 3).' '.date('Y');

        return view('sales::reports.index', compact(
            'totalTargets',
            'activeUsers',
            'avgAchievement',
            'currentPeriod'
        ));
    }

    /**
     * Target vs Actual report.
     */
    public function targetVsActual(Request $request): View
    {
        $filters = $request->only(['financial_year', 'period_type', 'user_id']);

        $reportData = $this->salesTargetRepository->getPerformanceSummary($filters);
        $users = $this->userRepository->all(['id', 'name']);

        return view('sales::reports.target-vs-actual', compact('reportData', 'filters', 'users'));
    }

    /**
     * Leaderboard report.
     */
    public function leaderboard(Request $request): View
    {
        $financialYear = $request->get('financial_year');

        $topPerformers = $this->salesTargetRepository
            ->with('user')
            ->when($financialYear, function ($query, $year) {
                return $query->where('financial_year', $year);
            })
            ->orderBy('achievement_percentage', 'desc')
            ->limit(20)
            ->get();

        return view('sales::reports.leaderboard', compact('topPerformers'));
    }

    /**
     * Forecast report.
     */
    public function forecast(): View
    {
        // Implementation for forecast report
        return view('sales::reports.forecast');
    }

    /**
     * Export reports.
     */
    public function export(string $type): Response
    {
        // Implementation for export functionality
        return response()->json(['message' => 'Export functionality coming soon']);
    }
}
