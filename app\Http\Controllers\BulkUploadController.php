<?php

namespace App\Http\Controllers;

use App\Imports\LeadsImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str; // Add Log facade
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException; // Specific exception for validation
use Throwable; // Import Throwable for broader catch

class BulkUploadController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        return view('leads.bulk-upload');
    }

    public function upload(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|mimes:xlsx,xls|max:5120',
            ]);

            $file = $request->file('file');
            $fileName = time().'_'.Str::slug($file->getClientOriginalName());
            $file->storeAs('uploads', $fileName, 'public');

            $import = new LeadsImport;
            Excel::import($import, storage_path('app/public/uploads/'.$fileName));

            // Check for errors collected by SkipsOnError implementation in LeadsImport
            // Note: LeadsImport doesn't seem to have an errors() method by default with SkipsOnError.
            // We rely on logging within LeadsImport::onError and the catch blocks below.

            return redirect()->route('admin.leads.bulk_upload.index')
                ->with('success', 'Leads import process completed. Check logs for any row-specific errors.');

        } catch (ValidationException $e) {
            Log::error('Bulk Lead Upload Validation Error: ', $e->failures());
            // Extract validation failures and present them
            $failures = $e->failures();
            $errorMessages = [];
            foreach ($failures as $failure) {
                $errorMessages[] = 'Row '.$failure->row().': '.implode(', ', $failure->errors()).' (Attribute: '.$failure->attribute().')';
            }

            return redirect()->route('admin.leads.bulk_upload.index')
                ->with('error', 'Validation errors occurred during import: <br>'.implode('<br>', $errorMessages));
        } catch (Throwable $e) { // Catch any other throwable error
            Log::error('Bulk Lead Upload General Error: '.$e->getMessage().' on line '.$e->getLine().' in '.$e->getFile());
            // Provide a more user-friendly message, potentially hinting at file format issues
            $errorMessage = 'An unexpected error occurred during import. Please check the file format, ensure data integrity (especially around row 2), and review the application logs for details. Error: '.$e->getMessage();

            return redirect()->route('admin.leads.bulk_upload.index')
                ->with('error', $errorMessage);
        }
    }

    public function showForm()
    {
        return view('leads.bulk-upload');
    }
}
