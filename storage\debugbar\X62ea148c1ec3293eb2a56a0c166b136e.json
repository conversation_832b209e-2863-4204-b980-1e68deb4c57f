{"__meta": {"id": "X62ea148c1ec3293eb2a56a0c166b136e", "datetime": "2025-07-01 02:55:54", "utime": 1751318754.277643, "method": "GET", "uri": "/admin/sales/reports/target-vs-actual", "ip": "127.0.0.1"}, "php": {"version": "8.2.25", "interface": "cli-server"}, "messages": {"count": 142, "messages": [{"message": "[02:55:53] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$app is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.1485, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:53] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$request is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 22", "message_html": null, "is_string": false, "label": "warning", "time": **********.148897, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.168673, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.169213, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.218836, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.219685, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.220313, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.220745, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.22132, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.221779, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.222671, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.223037, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.223547, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.223801, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.22442, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.224919, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.225565, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.225742, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.225981, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.226136, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.226369, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.22654, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.226779, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.226929, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.227154, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.227322, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.227579, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.227728, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.227974, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.228122, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.228374, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.22853, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.228762, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.228921, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.229156, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.229318, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.229567, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.229723, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.229962, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.230123, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.230363, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.230613, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.230844, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.231017, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.231244, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.231402, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.231644, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.231807, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.232071, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.232264, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.232546, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.232713, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.232927, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.233069, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.2333, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.233454, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.233681, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.233825, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.234038, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.234191, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.234408, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.234641, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.234867, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.235279, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.236136, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.236484, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.236771, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.236932, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.237169, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.237694, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.238201, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.238543, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.23908, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.239403, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.240054, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.241279, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.242207, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.242556, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.243078, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.243388, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.243877, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.244187, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.244682, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.245001, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.245559, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.24589, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.246401, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.24672, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.247258, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.247572, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.248071, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.248355, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.248815, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.249117, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.249614, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.249976, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.25053, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.250846, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.251332, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.252091, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.252658, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.253003, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.25354, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.253837, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.254336, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.254642, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.255107, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.25539, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.255807, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.255966, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.256221, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.256375, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.256633, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.257219, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.25832, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.258618, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.259043, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.259203, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.259703, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.260037, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.260516, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.26081, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.261317, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.261655, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.262168, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.262512, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.263136, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.263506, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.264027, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.264331, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.264819, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.265098, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.265595, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.265884, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.266363, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.266667, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.267173, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.267658, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.268306, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.268721, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 180", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.269328, "xdebug_link": null, "collector": "log"}, {"message": "[02:55:54] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\storage\\framework\\views\\6896026b2ae8c3c81c74c4c74f86a6d2.php on line 181", "message_html": null, "is_string": false, "label": "warning", "time": 1751318754.269788, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751318752.163161, "end": 1751318754.278093, "duration": 2.114932060241699, "duration_str": "2.11s", "measures": [{"label": "Booting", "start": 1751318752.163161, "relative_start": 0, "end": **********.063282, "relative_end": **********.063282, "duration": 0.9001209735870361, "duration_str": "900ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.063316, "relative_start": 0.9001550674438477, "end": 1751318754.278096, "relative_end": 2.86102294921875e-06, "duration": 1.2147798538208008, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 28433144, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "sales::reports.target-vs-actual", "param_count": null, "params": [], "start": **********.366802, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src/resources/views/reports/target-vs-actual.blade.phpsales::reports.target-vs-actual", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FResources%2Fviews%2Freports%2Ftarget-vs-actual.blade.php&line=1", "ajax": false, "filename": "target-vs-actual.blade.php", "line": "?"}}, {"name": "admin::layouts.content", "param_count": null, "params": [], "start": 1751318754.270397, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/layouts/content.blade.phpadmin::layouts.content", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Flayouts%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}}, {"name": "admin::layouts.nav-top", "param_count": null, "params": [], "start": 1751318754.271225, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src/resources/views/layouts/nav-top.blade.phpadmin::layouts.nav-top", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Flayouts%2Fnav-top.blade.php&line=1", "ajax": false, "filename": "nav-top.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/sales/reports/target-vs-actual", "middleware": "web, admin_locale, user", "controller": "Webkul\\Sales\\Http\\Controllers\\ReportController@targetVsActual", "namespace": null, "prefix": "admin/sales/reports", "where": [], "as": "admin.sales.reports.target_vs_actual", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FHttp%2FControllers%2FReportController.php&line=46\" onclick=\"\">packages/Webkul/Sales/src/Http/Controllers/ReportController.php:46-54</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05029, "accumulated_duration_str": "50.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 557}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}], "start": **********.181409, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:1152", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=1152", "ajax": false, "filename": "BaseRepository.php", "line": "1152"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `core_config` where `code` = 'general.general.locale_settings.locale'", "type": "query", "params": [], "bindings": ["general.general.locale_settings.locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Core.php", "line": 241}], "start": **********.1934621, "duration": 0.0065899999999999995, "duration_str": "6.59ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 13.104}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2356398, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 13.104, "width_percent": 3.957}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, {"index": 22, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "admin_locale", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.250755, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "user:56", "source": {"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=56", "ajax": false, "filename": "Bouncer.php", "line": "56"}, "connection": "laravel-crm", "explain": null, "start_percent": 17.061, "width_percent": 1.73}, {"sql": "select * from `crm_sales_targets`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/SalesTargetRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Repositories\\SalesTargetRepository.php", "line": 208}, {"index": 16, "namespace": null, "name": "packages/Webkul/Sales/src/Http/Controllers/ReportController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Http\\Controllers\\ReportController.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2614262, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "SalesTargetRepository.php:208", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/SalesTargetRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Repositories\\SalesTargetRepository.php", "line": 208}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FSalesTargetRepository.php&line=208", "ajax": false, "filename": "SalesTargetRepository.php", "line": "208"}, "connection": "laravel-crm", "explain": null, "start_percent": 18.791, "width_percent": 4.395}, {"sql": "select * from `users` where `users`.`id` in (1, 2, 3, 4, 5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/SalesTargetRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Repositories\\SalesTargetRepository.php", "line": 208}, {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Http/Controllers/ReportController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Http\\Controllers\\ReportController.php", "line": 50}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.276652, "duration": 0.01329, "duration_str": "13.29ms", "memory": 0, "memory_str": null, "filename": "SalesTargetRepository.php:208", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/SalesTargetRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Repositories\\SalesTargetRepository.php", "line": 208}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FSalesTargetRepository.php&line=208", "ajax": false, "filename": "SalesTargetRepository.php", "line": "208"}, "connection": "laravel-crm", "explain": null, "start_percent": 23.186, "width_percent": 26.427}, {"sql": "select * from `crm_sales_achievements` where `crm_sales_achievements`.`sales_target_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/SalesTargetRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Repositories\\SalesTargetRepository.php", "line": 208}, {"index": 21, "namespace": null, "name": "packages/Webkul/Sales/src/Http/Controllers/ReportController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Http\\Controllers\\ReportController.php", "line": 50}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.318521, "duration": 0.02443, "duration_str": "24.43ms", "memory": 0, "memory_str": null, "filename": "SalesTargetRepository.php:208", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/SalesTargetRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Repositories\\SalesTargetRepository.php", "line": 208}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FSalesTargetRepository.php&line=208", "ajax": false, "filename": "SalesTargetRepository.php", "line": "208"}, "connection": "laravel-crm", "explain": null, "start_percent": 49.612, "width_percent": 48.578}, {"sql": "select `id`, `name` from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}, {"index": 18, "namespace": null, "name": "packages/Webkul/Sales/src/Http/Controllers/ReportController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Sales\\src\\Http\\Controllers\\ReportController.php", "line": 51}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.34938, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "laravel-crm", "explain": null, "start_percent": 98.19, "width_percent": 1.81}]}, "models": {"data": {"Webkul\\Sales\\Models\\SalesTarget": {"value": 70, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FSalesTarget.php&line=1", "ajax": false, "filename": "SalesTarget.php", "line": "?"}}, "Webkul\\User\\Models\\User": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Webkul\\Sales\\Models\\SalesAchievement": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FSalesAchievement.php&line=1", "ajax": false, "filename": "SalesAchievement.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 92, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OgB8JfkX4ggZ6jcGErBS53AxTDOXqZKsrK7JkxHL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/sales/reports/target-vs-actual\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/admin/sales/reports/target-vs-actual", "status_code": "<pre class=sf-dump id=sf-dump-786984903 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-786984903\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-830185863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-830185863\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-66439436 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-66439436\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1182502759 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/admin/sales/reports</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IjlEc0hGY0lYRkVxcHJKdDlUS0dpTmc9PSIsInZhbHVlIjoiRVk2OTAxSFR6RFN3eFpkTzBsV0dodnFYY1p0ZEJ1ZG5zQjAyRjNZZVlpZlRWSGRSN0k4ODM0MkppdUkwNEp1S0IyTDhzTSsvTkVkRGpYdVhTSVVlbXhMR082Q2RzR3FjYllZbmlzU3BITDYzOFBBVG5lZTQ4bHU0VFpqeXA2QXYiLCJtYWMiOiJiOWEyMjI0MGZhOWE1ODRiOGU3NjJhMTY0ZTYwMGNmYjJiZDgyYmU5ZTE5MDgzZTQ1NWM5Yzc0ODRkNmY1NTc3IiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6IkNQUVdWWEJPb2xIeWNMMkFZS0ZTOHc9PSIsInZhbHVlIjoidjZ2bGd5NUNTWDl6aGdlaUFwekg4QWFXdk1SaVVraWhOMGJvcFljemFVSFRzWnh2S0wyL1M4dVlyeWl0aVFvNDZoTGZ4Z0k3RGRjd0RQVUF1U2hXRWVycGl2WGFRUzBvZUE0Z3RBZUVFK2c4K1MrOFl6RldPRE1PVnlQNVQzbkQiLCJtYWMiOiI0Yjc0ZjZiYjQzY2EwMzcyOGE5N2NhMmZmYmI1ZTY3ZTA3ODY4YWFmZjk5NTRlNDFjYThjZmI4NzdlOTYyYWQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182502759\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OgB8JfkX4ggZ6jcGErBS53AxTDOXqZKsrK7JkxHL</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UBUNbKSfVefd2B7hzIQg81JeYUMTvXps8tdyPKs1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1759767853 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 30 Jun 2025 21:25:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjYxL3ZPdW0xRmtHZm9PS1c2WVZRUHc9PSIsInZhbHVlIjoia3VaOEt0bFJWTDFKYnllSS9oSVprQXl2UFFzZHZPOTFVV2VCMElmZkFzZUsyS0dtNXpYRmxzRGs4dHo5VGpaWUduTE8rc25JQVI2MUI3Q2oyOTVrSWVid1BMOG91Wkd2YnNnb1JFclZaanpqVllId08rbzdxdXNma1JLTW5nd1IiLCJtYWMiOiJlZDZjZmQyZTdkYTY4NzI0NmFmN2Q2Yzk5NWFjOTc2ODU3OGQ0Nzg2NmNjYWRmYmRhZDUyNDE5ZjhlYzk2NTM0IiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 23:25:54 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">krayin_crm_session=eyJpdiI6IkVKdmRxc2hEN2Q5N3A5ZGxiSEZPSEE9PSIsInZhbHVlIjoiRWRGdlpvK1lpTXMwR2pQMzgzMHc5KzMrMTdvZFdPbjNTVlA0QlhidHhUdzJTSlJWS25rdU4rbVJJK3hQZGpId2tjcFFQaWZ4Y0FTU0QxWlVuajFINDBNZVNaY2FYZlpFRVhxZ1h4RExTbUg2MUlMM1BSKzNhUE4yWTRaVUlUMGwiLCJtYWMiOiJhYzFkZjI5MzNiYTk2MjdmNjQzZmZmNzAzMDNhNGZkMDgwNTM2ZDQzZGUyZDIwMmM3ODdiNzljMzI5M2Y5NTdmIiwidGFnIjoiIn0%3D; expires=Mon, 30 Jun 2025 23:25:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjYxL3ZPdW0xRmtHZm9PS1c2WVZRUHc9PSIsInZhbHVlIjoia3VaOEt0bFJWTDFKYnllSS9oSVprQXl2UFFzZHZPOTFVV2VCMElmZkFzZUsyS0dtNXpYRmxzRGs4dHo5VGpaWUduTE8rc25JQVI2MUI3Q2oyOTVrSWVid1BMOG91Wkd2YnNnb1JFclZaanpqVllId08rbzdxdXNma1JLTW5nd1IiLCJtYWMiOiJlZDZjZmQyZTdkYTY4NzI0NmFmN2Q2Yzk5NWFjOTc2ODU3OGQ0Nzg2NmNjYWRmYmRhZDUyNDE5ZjhlYzk2NTM0IiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 23:25:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">krayin_crm_session=eyJpdiI6IkVKdmRxc2hEN2Q5N3A5ZGxiSEZPSEE9PSIsInZhbHVlIjoiRWRGdlpvK1lpTXMwR2pQMzgzMHc5KzMrMTdvZFdPbjNTVlA0QlhidHhUdzJTSlJWS25rdU4rbVJJK3hQZGpId2tjcFFQaWZ4Y0FTU0QxWlVuajFINDBNZVNaY2FYZlpFRVhxZ1h4RExTbUg2MUlMM1BSKzNhUE4yWTRaVUlUMGwiLCJtYWMiOiJhYzFkZjI5MzNiYTk2MjdmNjQzZmZmNzAzMDNhNGZkMDgwNTM2ZDQzZGUyZDIwMmM3ODdiNzljMzI5M2Y5NTdmIiwidGFnIjoiIn0%3D; expires=Mon, 30-Jun-2025 23:25:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759767853\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-624222057 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OgB8JfkX4ggZ6jcGErBS53AxTDOXqZKsrK7JkxHL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"58 characters\">http://127.0.0.1:8000/admin/sales/reports/target-vs-actual</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624222057\", {\"maxDepth\":0})</script>\n"}}