{"__meta": {"id": "X5806f8ab44c66397e032de9b5ed2cc7b", "datetime": "2025-04-16 00:45:17", "utime": **********.993355, "method": "GET", "uri": "/admin/mail/inbox?pagination[page]=1&pagination[per_page]=10", "ip": "127.0.0.1"}, "php": {"version": "8.2.25", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[00:45:17] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$app is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.848749, "xdebug_link": null, "collector": "log"}, {"message": "[00:45:17] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$request is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 22", "message_html": null, "is_string": false, "label": "warning", "time": **********.848916, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.509181, "end": **********.993383, "duration": 0.48420190811157227, "duration_str": "484ms", "measures": [{"label": "Booting", "start": **********.509181, "relative_start": 0, "end": **********.805692, "relative_end": **********.805692, "duration": 0.2965109348297119, "duration_str": "297ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.805701, "relative_start": 0.2965199947357178, "end": **********.993386, "relative_end": 3.0994415283203125e-06, "duration": 0.1876850128173828, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 28863440, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/mail/{route?}", "middleware": "web, admin_locale, user", "controller": "Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@index", "namespace": null, "prefix": "admin/mail", "where": [], "as": "admin.mail.index", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FMail%2FEmailController.php&line=38\" onclick=\"\">packages/Webkul/Admin/src/Http/Controllers/Mail/EmailController.php:38-59</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.040209999999999996, "accumulated_duration_str": "40.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 557}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}], "start": **********.860096, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:1152", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=1152", "ajax": false, "filename": "BaseRepository.php", "line": "1152"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `core_config` where `code` = 'general.general.locale_settings.locale'", "type": "query", "params": [], "bindings": ["general.general.locale_settings.locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Core\\src\\Core.php", "line": 241}], "start": **********.86518, "duration": 0.02293, "duration_str": "22.93ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 57.026}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9101841, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 57.026, "width_percent": 1.567}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, {"index": 22, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "admin_locale", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.915821, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "user:56", "source": {"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=56", "ajax": false, "filename": "Bouncer.php", "line": "56"}, "connection": "laravel-crm", "explain": null, "start_percent": 58.592, "width_percent": 1.293}, {"sql": "select count(*) as aggregate from (select `emails`.`id`, `emails`.`name`, `emails`.`subject`, `emails`.`reply`, `emails`.`is_read`, `emails`.`created_at`, `tags`.`name` as `tags`, COUNT(DISTINCT email_attachments.id) as attachments from `emails` left join `email_attachments` on `emails`.`id` = `email_attachments`.`email_id` left join `email_tags` on `emails`.`id` = `email_tags`.`email_id` left join `tags` on `tags`.`id` = `email_tags`.`tag_id` where `folders` like '%\\\"inbox\\\"%' and `parent_id` is null group by `emails`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["%\"inbox\"%"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 371}, {"index": 17, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 386}, {"index": 18, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 425}, {"index": 19, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 578}, {"index": 20, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 279}], "start": **********.965301, "duration": 0.01613, "duration_str": "16.13ms", "memory": 0, "memory_str": null, "filename": "DataGrid.php:371", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/DataGrid/src/DataGrid.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\DataGrid\\src\\DataGrid.php", "line": 371}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FDataGrid%2Fsrc%2FDataGrid.php&line=371", "ajax": false, "filename": "DataGrid.php", "line": "371"}, "connection": "laravel-crm", "explain": null, "start_percent": 59.886, "width_percent": 40.114}]}, "models": {"data": {"Webkul\\User\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/mail/inbox\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/admin/mail/inbox", "status_code": "<pre class=sf-dump id=sf-dump-1928813093 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1928813093\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-468457241 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pagination</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>per_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468457241\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2106132814 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2106132814\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-487758427 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImZ6ZmZNaFZKRytuMnBkRGZ2YitQeGc9PSIsInZhbHVlIjoidUQ4VjYrMFQxamYxOE4wMmxtUHVVVlJ1YVhNMEFYWW1CbVc5NTNYNWdHeDNZWWpWSWhsM1dWcTNNRzZYeEpxUlcrRDhMRjFudi9CdGwwRDdMb0FvTG5MS28yaGtLVmNSblB5WmFpNmFCckhoVHk5OG9XTnNvY0hNdGp2TzI3WGsiLCJtYWMiOiJkYzZjNDk0YmYzOTJkZTk2NTVhYjA1YWI0YTEwNzQ4ZjI2Mzg0NTg3ZDY3ZmJiYzFjZDJjMTUyYWRkNmI1YjBiIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Brave&quot;;v=&quot;135&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/mail/inbox</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImZ6ZmZNaFZKRytuMnBkRGZ2YitQeGc9PSIsInZhbHVlIjoidUQ4VjYrMFQxamYxOE4wMmxtUHVVVlJ1YVhNMEFYWW1CbVc5NTNYNWdHeDNZWWpWSWhsM1dWcTNNRzZYeEpxUlcrRDhMRjFudi9CdGwwRDdMb0FvTG5MS28yaGtLVmNSblB5WmFpNmFCckhoVHk5OG9XTnNvY0hNdGp2TzI3WGsiLCJtYWMiOiJkYzZjNDk0YmYzOTJkZTk2NTVhYjA1YWI0YTEwNzQ4ZjI2Mzg0NTg3ZDY3ZmJiYzFjZDJjMTUyYWRkNmI1YjBiIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6ImpKU0F2Wlh2ZUZrNVhZdkMyd3grU0E9PSIsInZhbHVlIjoiZGhvSjNtUkRhUTJQWExtWFhiNm8wYkxCaU1DY1hxeTkxZUZXN3Q5MzZoUGFrQzlmenFXSk1lemRVWjltL2dpS1JraElEWHJIVzNWYWp5dzhTREZ6S0FtK0diVVFKM2ZMWm04TTR1OWQzM3BGaWRNcFVWTHlWZjNIQS81M3YzZGgiLCJtYWMiOiJmMTVlMzhmNGFhYjc3MDlmODJmM2ViNWNhMWRhMTNkZGVhMTlhNzhkOGY4OTY5MzJiZjM4Yjk2MmMwNGVhNzczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-487758427\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-221539214 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lh4xOQcW5AWaSgG8tSFW592msyWyo1WTTiE9zQSW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221539214\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2116067168 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Apr 2025 19:15:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImI1UjRPdy9tMXJFTFk0RnE4Ykp4c2c9PSIsInZhbHVlIjoiL1hVejhURTdWYytWcURPRGNtQnBXc1lkVHJ0VUZ4aktIWklGR095R05xYlI2WUhtTnlvTHpzZVhBMDhLQ2NBb2hSUk00T0R1VUVGenZzaHdRbHAwVHFTQ05pRWhmVEQ5QjE1T2hHbG5Xa2wwYldJM3VyQ05mM1BpY3NkbnI0cjIiLCJtYWMiOiIyZTZjYzY2MGFlOTEyNDNkZDViN2Q1MWJlYjkxMDdlNmM1YzI4MjJlNjJmZjkxM2QyYmE1YmM3NTA4NWFjMjNjIiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 21:15:17 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"446 characters\">krayin_crm_session=eyJpdiI6IlJCbmM3SmppcTlMdFZISkJsN3dMZWc9PSIsInZhbHVlIjoiQ05CMVNFRE1RVjdjK1JWSFpZSDR4R2dITlRxekRJdFd1eGEzSEp3UENUdTZYMytsZnNtRTcwK29zMUFOakdvSVZ5SWF2U2V3VkllcjN0NUlRRmFVSjVkb1k1ekoraGhlQjhmeG1QdDc2UDNhTjRYK2pnQTl4MDg1bld5S0lZYU8iLCJtYWMiOiIyMjgwMTAxNjZkMTg0ZGExOTU1MmQwMzE0OGZlODIwM2RkNTBjNjMyZDVlZTMyMmQwZjZjNTAwYzgxY2FmZjM4IiwidGFnIjoiIn0%3D; expires=Tue, 15 Apr 2025 21:15:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImI1UjRPdy9tMXJFTFk0RnE4Ykp4c2c9PSIsInZhbHVlIjoiL1hVejhURTdWYytWcURPRGNtQnBXc1lkVHJ0VUZ4aktIWklGR095R05xYlI2WUhtTnlvTHpzZVhBMDhLQ2NBb2hSUk00T0R1VUVGenZzaHdRbHAwVHFTQ05pRWhmVEQ5QjE1T2hHbG5Xa2wwYldJM3VyQ05mM1BpY3NkbnI0cjIiLCJtYWMiOiIyZTZjYzY2MGFlOTEyNDNkZDViN2Q1MWJlYjkxMDdlNmM1YzI4MjJlNjJmZjkxM2QyYmE1YmM3NTA4NWFjMjNjIiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 21:15:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"418 characters\">krayin_crm_session=eyJpdiI6IlJCbmM3SmppcTlMdFZISkJsN3dMZWc9PSIsInZhbHVlIjoiQ05CMVNFRE1RVjdjK1JWSFpZSDR4R2dITlRxekRJdFd1eGEzSEp3UENUdTZYMytsZnNtRTcwK29zMUFOakdvSVZ5SWF2U2V3VkllcjN0NUlRRmFVSjVkb1k1ekoraGhlQjhmeG1QdDc2UDNhTjRYK2pnQTl4MDg1bld5S0lZYU8iLCJtYWMiOiIyMjgwMTAxNjZkMTg0ZGExOTU1MmQwMzE0OGZlODIwM2RkNTBjNjMyZDVlZTMyMmQwZjZjNTAwYzgxY2FmZjM4IiwidGFnIjoiIn0%3D; expires=Tue, 15-Apr-2025 21:15:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116067168\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1248091070 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gdFlNur4B9LySz3EOFbhczIzlRKAST8HHayfIHxg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/mail/inbox</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248091070\", {\"maxDepth\":0})</script>\n"}}