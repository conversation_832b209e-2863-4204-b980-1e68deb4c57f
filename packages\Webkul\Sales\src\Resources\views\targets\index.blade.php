@extends('admin::layouts.content')

@section('page_title')
    {{ __('sales::app.targets.title') }}
@stop

@section('content')
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1>{{ __('sales::app.targets.title') }}</h1>
                <p>{{ __('sales::app.targets.description') }}</p>
            </div>
            
            <div class="page-action">
                <a href="{{ route('admin.sales.targets.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {{ __('sales::app.targets.create') }}
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="page-content">
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.sales.targets.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="financial_year">Financial Year</label>
                                    <select name="financial_year" id="financial_year" class="form-control">
                                        <option value="">All Years</option>
                                        @if(isset($financialYears))
                                            @foreach($financialYears as $year)
                                                <option value="{{ $year }}" {{ ($filters['financial_year'] ?? '') == $year ? 'selected' : '' }}>
                                                    {{ $year }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="period_type">Period Type</label>
                                    <select name="period_type" id="period_type" class="form-control">
                                        <option value="">All Periods</option>
                                        <option value="quarter" {{ ($filters['period_type'] ?? '') == 'quarter' ? 'selected' : '' }}>Quarter</option>
                                        <option value="month" {{ ($filters['period_type'] ?? '') == 'month' ? 'selected' : '' }}>Month</option>
                                        <option value="half_year" {{ ($filters['period_type'] ?? '') == 'half_year' ? 'selected' : '' }}>Half Year</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="user_id">Sales Representative</label>
                                    <select name="user_id" id="user_id" class="form-control">
                                        <option value="">All Users</option>
                                        @if(isset($users))
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}" {{ ($filters['user_id'] ?? '') == $user->id ? 'selected' : '' }}>
                                                    {{ $user->name }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter"></i> Apply Filters
                                        </button>
                                        <a href="{{ route('admin.sales.targets.index') }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Targets Table -->
            @if(isset($targets) && count($targets) > 0)
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Sales Targets</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sales Rep</th>
                                        <th>Period</th>
                                        <th>Target Amount</th>
                                        <th>Current Achievement</th>
                                        <th>Progress</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($targets as $target)
                                        <tr>
                                            <td>{{ $target->user->name ?? 'N/A' }}</td>
                                            <td>
                                                {{ ucfirst(str_replace('_', ' ', $target->period_type)) }} 
                                                {{ $target->period_value }}, {{ $target->financial_year }}
                                            </td>
                                            <td>{{ number_format($target->target_amount, 2) }}</td>
                                            <td>{{ number_format($target->actual_amount ?? 0, 2) }}</td>
                                            <td>
                                                @php
                                                    $percentage = $target->target_amount > 0 ? (($target->actual_amount ?? 0) / $target->target_amount) * 100 : 0;
                                                @endphp
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-{{ $percentage >= 100 ? 'success' : ($percentage >= 75 ? 'warning' : 'danger') }}" 
                                                         style="width: {{ min($percentage, 100) }}%">
                                                        {{ number_format($percentage, 1) }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($percentage >= 100)
                                                    <span class="badge badge-success">Target Met</span>
                                                @elseif($percentage >= 75)
                                                    <span class="badge badge-warning">On Track</span>
                                                @else
                                                    <span class="badge badge-danger">Below Target</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.sales.targets.edit', $target->id) }}" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="{{ route('admin.sales.targets.destroy', $target->id) }}" 
                                                          style="display: inline;" 
                                                          onsubmit="return confirm('Are you sure you want to delete this target?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        @if(method_exists($targets, 'links'))
                            <div class="mt-3">
                                {{ $targets->links() }}
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-bullseye fa-3x text-muted mb-3"></i>
                        <h5>No Targets Found</h5>
                        <p class="text-muted">No sales targets have been set up yet.</p>
                        <a href="{{ route('admin.sales.targets.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create First Target
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@stop

@push('css')
<style>
    .progress {
        background-color: #e9ecef;
    }
    
    .btn-group .btn {
        margin-right: 2px;
    }
    
    .btn-group .btn:last-child {
        margin-right: 0;
    }
</style>
@endpush
