<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('crm_sales_achievements', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->unsigned();
            $table->integer('sales_target_id')->unsigned();
            $table->date('achievement_date');
            $table->enum('achievement_type', ['revenue', 'leads', 'deals']);
            $table->decimal('achievement_value', 15, 2);
            $table->string('source_type')->nullable(); // 'lead', 'quote', 'manual'
            $table->integer('source_id')->unsigned()->nullable(); // ID of the lead/quote
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Additional data like commission, etc.
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('sales_target_id')->references('id')->on('crm_sales_targets')->onDelete('cascade');
            
            $table->index(['user_id', 'achievement_date']);
            $table->index(['sales_target_id', 'achievement_type']);
            $table->index(['achievement_date', 'achievement_type']);
            $table->index(['source_type', 'source_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('crm_sales_achievements');
    }
};
